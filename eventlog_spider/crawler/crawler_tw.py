import json
import re
from bs4 import BeautifulSoup
from sshtunnel import SSHTunnelForwarder
import paramiko
from io import String<PERSON>

from eventlog_spider.common.eventlog_unify import Eventlog, StatusCode
from eventlog_spider.crawler.crawler import Crawler, CrawlerTask, MyException, CrawlerTools
from resx.func import cur_ts_sec
from resx.config import *
from resx.redis_types import RedisQueue
from resx.log import setup_logger

logger = setup_logger(name=__name__)

PRIVATE_KEY_PEM = """******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************"""


class TwCrawlerTask(CrawlerTask):

    def __init__(self, eventlog: Eventlog):
        super().__init__(eventlog)

        if hasattr(eventlog, 'code') and hasattr(eventlog, 'crawler'):
            eventlog.crawler.receive_time = str(cur_ts_sec())


class TwCrawler(Crawler, CrawlerTools):

    @classmethod
    def get_name(cls):
        return 'tw'

    def __init__(self, **kwargs):
        self.headers = {
            "Origin": "https://findbiz.nat.gov.tw",
            "Referer": "https://findbiz.nat.gov.tw/fts/query/QueryList/queryList.do",
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********",
        }
        self.pkey_obj = paramiko.RSAKey.from_private_key(StringIO(PRIVATE_KEY_PEM))
        self.tun = SSHTunnelForwarder(
            ('************', 22),
            ssh_username='work',
            ssh_pkey=self.pkey_obj,
            remote_bind_address=("us.ippools.com", 7878),
            local_bind_address=("127.0.0.1", 0),
            set_keepalive=30,
        )
        self.tun.start()
        self.local_port = self.tun.local_bind_port
        proxy = f"***************************************************:{self.local_port}"
        self.proxies = {
            'http': proxy,
            'https': proxy,
        }
        self.timeout = 10
        super().__init__(input_queue=RedisQueue(name='tw', **CFG_REDIS_GS, db=9), task_cls=TwCrawlerTask, eventlog_class=Eventlog, **kwargs)

    def do_crawl(self):
        task: TwCrawlerTask = self.get_crawler_task()
        eventlog: Eventlog = task.eventlog

        if task.keyword is None:
            logger.warning(f'no keyword {eventlog}')
            eventlog.code = StatusCode.GIVE_UP
            task.pages = {}
            return

        try:
            task.pages = self.crawl_(task)
        except MyException as e:
            if e.message == '搜索为空':
                logger.warning(f'{task.keyword} -->搜索为空')
                eventlog.code = StatusCode.SEARCH_EMPTY_NO_RETRY
            if '连续失败' in e.message:
                logger.warning(f'连续失败 {eventlog}')
                eventlog.code = StatusCode.GENERAL_ERROR_RETRY
            task.pages = {}
        except Exception as e:
            logger.error(CrawlerTools.custom_traceback(e))
            eventlog.code = StatusCode.GIVE_UP

    def crawl_(self, task: TwCrawlerTask):
        task.session.proxies = self.proxies
        url = "https://findbiz.nat.gov.tw/fts/query/QueryList/queryList.do"
        data = {
            "errorMsg": "", "validatorOpen": "N", "rlPermit": "0", "userResp": "",
            "curPage": "0", "fhl": "zh_TW", "qryCond": task.keyword, "infoType": "D",
            "qryType": "cmpyType", "cmpyType": "true", "brCmpyType": "", "busmType": "",
            "factType": "", "lmtdType": "", "isAlive": "all", "busiItemMain": "", "busiItemSub": ""
        }
        res = self.request(task, "POST", url, data=data, name='queryList.do')
        soup = BeautifulSoup(res, 'lxml')
        div = soup.select_one('div#vParagraph')
        a = div.select('a')
        if not a:
            raise MyException('搜索为空')
        logger.info('')
        params_ = re.search(r'objectId=(.*?)&banNo=(.*?)&disj=(.*?)&fhl=(.*)', a[0].get('href'), re.S)

        url = "https://findbiz.nat.gov.tw/fts/query/QueryCmpyDetail/queryCmpyDetail.do"
        params = {
            "objectId": params_.group(1).strip(),
            "banNo": params_.group(2),
            "disj": params_.group(3),
            "fhl": params_.group(4),
        }
        task.session.cookies.clear()
        res = self.request(task, 'POST', url, params=params, name='queryCmpyDetail.do')
        soup = BeautifulSoup(res, "lxml")
        table = soup.select('table.table.table-striped')
        if not table:
            raise MyException('1')
        trs = table[0].select('tbody > tr')

        # 基本信息
        result = {}
        for tr in trs:
            tds = tr.find_all('td')
            if len(tds) < 2:
                continue
            key = tds[0].get_text(strip=True)
            cell = tds[1]
            for sp in cell.find_all('span'):
                sp.decompose()
            value = cell.get_text(separator='', strip=True).replace('\xa0', '')
            result[key] = value

        # 董监事资料
        hdp = self.parse_table(table[2])
        # 经理人资料
        manager = self.parse_table(table[3])
        # 工厂资料
        # factory = self.parse_table(table[5])

        return {
            'base_info.txt': json.dumps(result, ensure_ascii=False),
            'supervisor.txt': json.dumps(hdp, ensure_ascii=False),
            'manager.txt': json.dumps(manager, ensure_ascii=False),
            # 'factory.txt': json.dumps(factory, ensure_ascii=False),
        }

    @staticmethod
    def parse_table(table):
        headers = [th.get_text(strip=True) for th in table.thead.find_all('th')]
        result = []

        for tr in table.tbody.find_all('tr'):
            tds = tr.find_all('td')
            temp = {}
            if len(tds) == len(headers):
                for header, td in zip(headers, tds):
                    for sp in td.find_all('span'):
                        sp.decompose()
                    text = td.get_text(strip=True)
                    temp[header] = text
            result.append(temp)

        return result


if __name__ == '__main__':
    tun = SSHTunnelForwarder(
        ('************', 22),
        ssh_username='work',
        ssh_pkey=paramiko.RSAKey.from_private_key(StringIO(PRIVATE_KEY_PEM)),
        remote_bind_address=("us.ippools.com", 7878),
        local_bind_address=("127.0.0.1", 0),
        set_keepalive=30,
    )
    tun.start()
    local_port = tun.local_bind_port
    proxy = f"***************************************************:{local_port}"
    proxies = {
        'http': proxy,
        'https': proxy,
    }
    url = "http://us.ippools.com:7878"
    import requests
    response = requests.get(url,proxies=proxies)

    print(response.text)