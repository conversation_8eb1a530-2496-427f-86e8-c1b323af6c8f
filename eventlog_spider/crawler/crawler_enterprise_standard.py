import json
from urllib.parse import quote
import re
from bs4 import BeautifulSoup
import time
import copy

from eventlog_spider.common.eventlog_unify import Eventlog, StatusCode
from eventlog_spider.crawler.crawler import Crawler, CrawlerTask, MyException, CrawlerTools
from resx.func import cur_ts_sec
from resx.log import setup_logger
from resx.redis_types import RedisQueue
from resx.config import *
from eventlog_spider.scripts.order_char.main import main

logger = setup_logger(name=__name__)


class EnterpriseStandardCrawlerTask(CrawlerTask):

    def __init__(self, eventlog: Eventlog):
        super().__init__(eventlog)
        if hasattr(eventlog, 'code') and hasattr(eventlog, 'crawler'):
            eventlog.crawler.receive_time = str(cur_ts_sec())


class EnterpriseStandardCrawler(Crawler, CrawlerTools):
    @classmethod
    def get_name(cls):
        return 'enterprise_standard'

    def __init__(self, **kwargs):
        self.timeout = 3
        self.headers = {
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* "
                          "Safari/537.36",
        }
        self.LABEL_KEYS = ["企业名称", "统一代码", "标准名称", "标准编号", "发布时间", "状态"]
        super().__init__(input_queue=RedisQueue(name='enterprise_standard', **CFG_REDIS_GS, db=9), task_cls=EnterpriseStandardCrawlerTask,
                         eventlog_class=Eventlog, **kwargs)

    def do_crawl(self):
        task: EnterpriseStandardCrawlerTask = self.get_crawler_task()
        task.pages = {}
        eventlog: Eventlog = task.eventlog

        if task.keyword is None:
            logger.warning(f'no keyword {eventlog}')
            eventlog.code = StatusCode.GIVE_UP

        try:
            task.pages = self.crawl_(task)
        except MyException as e:
            if e.message == '搜索为空':
                logger.warning(f'{task.keyword} --> 搜索为空')
                eventlog.code = StatusCode.SEARCH_EMPTY_NO_RETRY
            if e.message == '接口连续失败':
                logger.warning(f'{task.keyword} --> 接口连续失败')
                eventlog.code = StatusCode.GENERAL_ERROR_RETRY
        except Exception as e:
            raise e

    def crawl_(self, task: EnterpriseStandardCrawlerTask):
        url = f"https://www.qybz.org.cn/gc/geetest/query?t={int(time.time() * 1000)}"
        for _ in range(20):
            try:
                res = self.request(task, 'GET', url, tojson=True)
                validate, challenge = main(json.loads(res))
                break
            except Exception as e:
                logger.warning(f'{task.keyword} --> {e}')

        url = "https://www.qybz.org.cn/user/searchR"
        params = {
            "keyword": quote(task.keyword), "pageNo": "1", "standardType": "4",
            "geetest_challenge": challenge,
            "geetest_validate": validate,
            "geetest_seccode": f"{validate}|jordan"
        }
        res = self.request(task, 'GET', url, params=params)
        list_ = self.parse_info_list(res)
        list2 = copy.deepcopy(list_)

        pageNo = 1
        while len(list_) == 10:
            params = {"keyword": quote(task.keyword), "pageNo": str(pageNo + 1), "standardType": "4"}
            res = self.request(task, 'GET', url, params=params)
            list_ = self.parse_info_list(res)
            list2.extend(list_)

        standards = []
        for item in list2:
            std_id = item.get("标准ID")
            detail_url = f"https://www.qybz.org.cn/user/detail/{std_id}"
            detail_res = self.request(task, 'GET', detail_url, name=f'detail-{std_id}')
            time.sleep(1)
            detail_data: dict = self.parse_detail_html(detail_res)
            detail_data['standard.txt']['状态'] = item['状态']
            standards.append(detail_data)
        return {'standards.txt': json.dumps(standards, ensure_ascii=False)}

    def parse_detail_html(self, html: str):
        soup = BeautifulSoup(html, "lxml")

        # 定位三个模块块
        def find_block(title):
            for blk in soup.select("div.table-datas"):
                if self.text_join(blk.select_one(".heading")) == title:
                    return blk
            return None

        blk_basic = find_block("企业基本信息")
        blk_std = find_block("标准信息")
        blk_prod = find_block("执行该标准的产品信息")

        result = {
            "base_info.txt": self.parse_key_value_table(blk_basic.find("table")) if blk_basic else {},
            "standard.txt": self.parse_key_value_table(blk_std.find("table")) if blk_std else {},
            "products.txt": self.parse_products_table(blk_prod) if blk_prod else [],
        }
        return result

    def parse_info_list(self, html: str):
        soup = BeautifulSoup(html, "lxml")  # 如无lxml也可用 "html.parser"
        result = []

        for block in soup.select("div.info-list > div.info-item"):
            record = {k: "" for k in self.LABEL_KEYS}
            std_id = None

            # 遍历每一行；每行可能包含多对 strong/值
            for row in block.select("div.row"):
                # 找到所有 strong 标签作为“键”
                for strong in row.find_all("strong"):
                    label = strong.get_text(strip=True)
                    if label not in self.LABEL_KEYS:
                        continue

                    # strong 的父节点是 label 容器(div.col-md-1 ...)，值在后一个 div.col-md-5 ...
                    label_div = strong.parent
                    value_div = self._next_value_div(label_div)
                    record[label] = self._compact_text_for_label(label, value_div)

                    # 可选：从值里的 <a href="javascript:toDetail('...')"> 抽取标准ID
                    if value_div:
                        a = value_div.find("a", href=True)
                        if a and not std_id:
                            m = re.search(r"toDetail\('([^']+)'\)", a["href"])
                            if m:
                                std_id = m.group(1)

            # 兜底：若状态未被上述逻辑抓到，尝试直接读 badge
            if not record["状态"]:
                badge = block.select_one("span.badge")
                if badge:
                    record["状态"] = badge.get_text(strip=True)

            if std_id:
                record["标准ID"] = std_id

            result.append(record)

        return result

    def parse_key_value_table(self, table):
        """
        解析“企业基本信息/标准信息”这类 KV 表格。
        规则：每个 label 的 <td> 带 class="td-label"，其**后一个** <td> 是值；
              兼容值单元格的 colspan（如“公开时间”、“查看文本”）。
        """
        data = {}
        for tr in table.select("tbody > tr"):
            tds = tr.find_all("td")
            i = 0
            while i < len(tds):
                td = tds[i]
                if "td-label" in (td.get("class") or []):
                    label = self.text_join(td)
                    value = ""
                    if i + 1 < len(tds):
                        value_cell = tds[i + 1]
                        if label == "查看文本":
                            a_tag = value_cell.find("a", href=True)
                            if not a_tag or not a_tag.has_attr("href"):
                                return ""
                            m = re.search(r"toPdfDetail\('([^']+)'\)", a_tag["href"])
                            value = m.group(1) if m else ""
                        else:
                            value = self.text_join(value_cell)

                        # 跳过被合并的列（colspan），避免错位
                        span = int(value_cell.get("colspan") or 1)
                        i += span
                    data[label] = value
                i += 1
        return data

    def parse_products_table(self, container):
        """
        解析“执行该标准的产品信息”：
          - thead 的 th 作为键
          - tbody 的每一行（td/th 混用）映射为 dict
        """
        table = container.find("table")
        headers = [self.text_join(th) for th in table.select("thead th")]
        rows = []
        for tr in table.select("tbody tr"):
            cells = tr.find_all(["td", "th"])
            if not cells:
                continue
            row = {headers[i]: self.text_join(cells[i]) if i < len(cells) else "" for i in range(len(headers))}
            rows.append(row)
        return rows

    @staticmethod
    def _compact_text_for_label(label: str, node) -> str:
        if not node:
            return ""
        parts = list(node.stripped_strings)
        if label in ("企业名称", "标准名称"):
            return "".join(parts)  # 中文名、标题等不应被空格打断
        return " ".join(parts)  # 编号/时间/状态等保留合理空格

    @staticmethod
    def _next_value_div(label_div):
        sib = label_div.next_sibling
        while sib and (getattr(sib, "name", None) != "div" or "col-md-5" not in (sib.get("class") or [])):
            sib = sib.next_sibling
        return sib

    @staticmethod
    def text_join(node):
        if not node:
            return ""
        return " ".join(s.strip() for s in node.stripped_strings)
