# encoding=utf8
import json
import logging
import time
from collections import OrderedDict
from typing import Union

import requests
from biz_utils.entity.company import CompanyDao
from requests import Response
from requests.adapters import HTTPAdapter

from eventlog_spider.common.eventlog_unify import Eventlog, StatusCode
from eventlog_spider.crawler.crawler import Crawler, CrawlerTask, MyException

logger = logging.getLogger(__name__)


class APP2CrawlerTask(CrawlerTask):
    def __init__(self, eventlog: Eventlog):
        self.search = eventlog.selector.get_info('keyword')
        self.session = requests.session()
        self.session.proxies = {
            'http': 'http://************:30636',
            'https': 'http://************:30636'
        }
        self.session.mount('http://', HTTPAdapter(max_retries=1))
        self.session.mount('https://', HTTPAdapter(max_retries=1))
        super().__init__(eventlog)


class APP2Crawler(Crawler):
    def __init__(self, name: str, **kwargs):
        self.company_dao = CompanyDao()
        self.timeout = 15
        self.headers = {
            "Host": "app.gsxt.gov.cn",
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 MicroMessenger/7.0.20.1781(0x6700143B) NetType/WIFI MiniProgramEnv/Windows WindowsWechat/WMPF WindowsWechat(0x63090b19)XWEB/11275",
            "Referer": "https://servicewechat.com/wx5b0ed3b8c0499950/15/page-frame.html",
        }
        super().__init__(name, task_cls=APP2CrawlerTask, **kwargs)

    def do_crawl(self):
        task: APP2CrawlerTask = self.get_crawler_task()
        eventlog: Eventlog = task.eventlog
        if task.search is None:
            logger.warning(f'no keyword {task.event_id}')
            eventlog.spider_code = StatusCode.GIVE_UP
            return
        try:
            task.pages = self.crawl_(task)
        except MyException as e:
            if e.message == '搜索为空':
                logger.debug(f'搜索为空')
                eventlog.spider_code = StatusCode.SEARCH_EMPTY_NO_RETRY
            if '连续失败' in e.message:
                logger.debug(f'{e.message}')
                eventlog.spider_code = StatusCode.GENERAL_ERROR_RETRY
            task.pages = {}
            return
        except Exception as e:
            raise e

    def crawl_(self, task: APP2CrawlerTask):
        pages = {}
        param = self.search(task)
        if not param:
            raise MyException('搜索为空')
        base_info = self.detail(task, param[0])
        investor = self.shareholder(task, param[0])
        staff = self.staff(task, param[0])
        pages['base_info.txt'] = json.dumps(base_info, ensure_ascii=False)
        pages['investor_info.txt'] = json.dumps(investor, ensure_ascii=False)
        pages['staff_info.txt'] = json.dumps(staff, ensure_ascii=False)
        return pages

    def detail(self, task: APP2CrawlerTask, param):
        url = f"https://app.gsxt.gov.cn/gsxt/corp-query-entprise-info-primaryinfoapp-entbaseInfo-{param['pripid']}.html"
        params = {"nodeNum": param['nodeNum'], "entType": param['entType'], "sourceType": "W"}
        res: dict = self.request(task, url, "POST", params=params, name='base_info')
        return res

    def shareholder(self, task: APP2CrawlerTask, param):
        url = f"https://app.gsxt.gov.cn/gsxt/corp-query-entprise-info-shareholder-{param['pripid']}.html"
        params = {"nodeNum": param['nodeNum'], "entType": "1", "sourceType": "W"}
        list_ = []
        res: dict = self.request(task, url, "GET", params=params, name='shareholder')
        list_.extend(res['data'])
        total = res['recordsTotal']
        for i in range(5, total, 5):
            params.update({"start": i})
            res: dict = self.request(task, url, "GET", params=params, name='shareholder')
            list_.extend(res['data'])
        # list_u = self.unique(list_, ['invId', 'url'])
        # if len(list_u) != total:
        #     raise MyException('连续失败')
        return list_

    def staff(self, task: APP2CrawlerTask, param):
        url = f"https://app.gsxt.gov.cn/gsxt/corp-query-entprise-info-KeyPerson-{param['pripid']}.html"
        params = {"nodeNum": param['nodeNum'], "entType": "1", "sourceType": "W"}
        res: dict = self.request(task, url, "GET", params=params, name='staff')
        return res['data']

    def search(self, task: APP2CrawlerTask) -> dict:
        url = "https://app.gsxt.gov.cn/gsxt/corp-query-app-search-1.html"
        data = {
            "conditions": "{\"excep_tab\":\"0\",\"ill_tab\":\"0\",\"area\":\"0\",\"cStatus\":\"0\",\"xzxk\":\"0\",\"xzcf\":\"0\",\"dydj\":\"0\"}",
            "searchword": task.search,
            "sourceType": "W"
        }
        res: dict = self.request(task, url, "POST", data=data, name='search')
        return res['data']['result']['data']

    def request(self, task: APP2CrawlerTask, url: str, method: str, params: dict = None, data: dict = None, json: dict = None,
                path: str = '', name: str = '', toRaw=False, toText=False) -> Union[dict, Response, str]:
        for _ in range(20):
            response = None
            try:
                a = time.time()
                response = task.session.request(**{'method': method, 'url': url, 'data': data, 'headers': self.headers, 'json': json,
                                                   'params': params, 'verify': False, 'timeout': self.timeout})
                logger.info(f'{task.search} --> {name} - {response.status_code} - time:{time.time() - a}')
                if response.status_code != 200 or '操作过于频繁' in response.text or '{}' == response.text:
                    logger.warning(f'{task.search} --> {name} - {response.status_code}')
                    # del task.session.cookies['proxyBase']
                    task.session.cookies.clear()
                    continue
                if toRaw:
                    return response
                if toText:
                    return response.text
                if name in ['search'] and not response.json()['data']:
                    logger.warning(f'{task.search} --> {name} - {response.status_code}')
                    # del task.session.cookies['proxyBase']
                    task.session.cookies.clear()
                    continue
                logger.info(f"{task.search} --> {path.split('/')[-1]}:{name}({_ + 1}) --> {response.json()}")
                return response.json()
            except (requests.exceptions.ConnectionError, requests.exceptions.ProxyError, requests.exceptions.Timeout) as e:
                logger.warning(f"{task.search} --> {path.split('/')[-1]}:{name}({_ + 1}) --> 代理问题 重试：{e}")
                # del task.session.cookies['proxyBase']
                task.session.cookies.clear()
            except Exception as e:
                html = response.text if response else ''
                status_code = response.status_code if response else ''
                logger.error(f"{task.search} --> {name}-{status_code} - html: {html}")
                # del task.session.cookies['proxyBase']
                task.session.cookies.clear()
        raise MyException('接口连续失败')

    @staticmethod
    def unique(my_list, exclude_field: list[str]):
        unique_list = []
        history = {}
        for d in my_list:
            filtered_items = tuple((k, v) for k, v in sorted(d.items()) if k not in exclude_field)
            unique_list.append(filtered_items)
            history.update({d[f"inv_{i}"]: d[i] for i in exclude_field})
        unique_list = list(OrderedDict.fromkeys(unique_list))
        data = []
        for t in unique_list:
            d = dict(t)
            d.update({k: history[d[f'inv_{k}']] for k in exclude_field})
            data.append(d)
        return data
