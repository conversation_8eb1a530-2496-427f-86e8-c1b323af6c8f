# encoding=utf8
import json
import argparse
import time
from concurrent.futures import Future
from resx.log import setup_logger

ap = argparse.ArgumentParser(description='通用爬虫-抓取程序 抓取数据到OBS 并发送Kafka消息')
ap.add_argument('--worker-num', type=int, default=1, help='worker数量')
ap_args = ap.parse_args()
logger = setup_logger(use_crawler_log=True, name='eventlog-crawler', debug=False)
from resx.func import get_stack_info, cur_ts_sec, get_my_ip
from resx.tools import BoundedExecutor
from resx.redis_types import RedisQueue
from resx.config import *
from resx.redis_types import RedisRegister
from resx.func import get_env
from eventlog_spider.common.eventlog import Eventlog, SpiderCode
from eventlog_spider.common.eventlog_unify import StatusCode
from eventlog_spider.crawler.crawler import Crawler
from eventlog_spider.crawler.crawler_gs_jx import GSJXCrawler
from eventlog_spider.crawler.crawler_gs_js import GSJSCrawler
from eventlog_spider.crawler.crawler_gs_heilongjiang import GSHeiLongJiangCrawler
from eventlog_spider.crawler.crawler_gs_hainan import GSHaiNanCrawler
from eventlog_spider.crawler.crawler_gs_tianjing import TJCrawler
from eventlog_spider.crawler.crawler_gs_zhuhai import ZhuhaiCrawler
from eventlog_spider.crawler.crawler_gs_xiamen import XiamenCrawler
from eventlog_spider.crawler.crawler_gs_shanghai import ShanghaiCrawler
from eventlog_spider.crawler.crawler_gs_gdgz import GDGZCrawler
from eventlog_spider.crawler.crawler_gs_beijing import BeiJingCrawler
from eventlog_spider.crawler.crawler_gs_sichuan_chengdu import ChengduCrawler
from eventlog_spider.crawler.crawler_gs_guangdong import GSGuangDongCrawler
from eventlog_spider.crawler.crawler_gs_guizhou import GSGuiZhouCrawler
from eventlog_spider.crawler.crawler_acftu import ACFTUCrawler
from eventlog_spider.crawler.crawler_npo import CrawlerNpo
from eventlog_spider.crawler.crawler_law import CrawlerLaw
from eventlog_spider.crawler.crawler_cods import CodsCrawler
from eventlog_spider.crawler.crawler_foundation import FoundationCrawler
from eventlog_spider.crawler.crawler_hk import CrawlerHK
from eventlog_spider.crawler.crawler_gov_unit import GovUnitCrawler
from eventlog_spider.crawler.crawler_gds import GdsCrawler
from eventlog_spider.crawler.crawler_tw import TwCrawler
from eventlog_spider.crawler.crawler_enterprise_standard import EnterpriseStandardCrawler


def process(crawlers_: dict[str, Crawler], crawler_register: RedisRegister, crawler_output_queue: RedisQueue):
    known_crawler_names = list(crawlers_.keys())
    crawler_name, last_register_ts = '', 0
    while True:
        if not crawler_name or last_register_ts + 10 < cur_ts_sec():
            last_register_ts = cur_ts_sec()
            try:
                crawler_name, crawler_params = crawler_register.register_task(known_tasks=known_crawler_names)
            except Exception as e:
                logger.warning(f'error register {e} {get_stack_info()}')
        if not crawler_name:
            logger.info(f'no crawler_name, sleep for next register')
            time.sleep(2)
            continue
        if crawler_name not in crawlers_:
            logger.warning(f'crawler_name={crawler_name} not in crawlers')
            time.sleep(5)
            continue
        crawler = crawlers_[crawler_name]

        s = crawler.input_queue.pop()
        if not s:
            logger.info(f'empty queue {crawler.input_queue.name}, sleep for next register')
            time.sleep(2)
            continue

        try:
            d = json.loads(s)
            eventlog: Eventlog = crawler.eventlog_class.from_dict(d)
            if not eventlog:
                logger.warning(f'error from dict {s}')
                continue
        except Exception as e:
            logger.warning(f'error eventlog {e}')
            time.sleep(2)
            continue
        try:
            crawler.crawl(eventlog)
        except Exception as e:
            if hasattr(eventlog, 'spider_code'):
                eventlog.spider_code = SpiderCode.GIVE_UP
            else:
                eventlog.code = StatusCode.GIVE_UP
            logger.info(f'error process {eventlog} set SpiderCode.GIVE_UP {e} {get_stack_info()}')
        eventlog_str = eventlog.to_json()
        ret = crawler_output_queue.push(eventlog_str)
        logger.info(f'OUTPUT {eventlog_str} ret={ret}')


def callback_fn(f: Future):
    try:
        f.result()
    except Exception as e:
        logger.warning(f'CRAWLER FAIL {e} {get_stack_info()}')


if __name__ == '__main__':
    if get_my_ip() == '*************':
        logger.info(f'tw ip: {get_my_ip()}')
        crawler_register = RedisRegister(name=f'eventlog_spider:crawler_threads_set_tw', db=3, **CFG_REDIS_GS)
        crawler_output_queue = RedisQueue(name=f'eventlog_spider:crawler_output.tw', db=3, **CFG_REDIS_GS)
        crawlers = {
            TwCrawler.get_name(): TwCrawler(),
        }
    else:
        crawler_register = RedisRegister(name=f'eventlog_spider:crawler_threads_set_{get_env().value}', db=3, **CFG_REDIS_GS)
        crawler_output_queue = RedisQueue(name=f'eventlog_spider:crawler_output.{get_env().value}', db=3, **CFG_REDIS_GS)
        crawlers = {
            EnterpriseStandardCrawler.get_name(): EnterpriseStandardCrawler(),
            GdsCrawler.get_name(): GdsCrawler(),
            ACFTUCrawler.get_name(): ACFTUCrawler(),
            CrawlerNpo.get_name(): CrawlerNpo(),
            CrawlerLaw.get_name(): CrawlerLaw(),
            GSJXCrawler.get_name(): GSJXCrawler(),
            GSJSCrawler.get_name(): GSJSCrawler(),
            GSHeiLongJiangCrawler.get_name(): GSHeiLongJiangCrawler(),
            GSHaiNanCrawler.get_name(): GSHaiNanCrawler(),
            CodsCrawler.get_name(): CodsCrawler(),
            FoundationCrawler.get_name(): FoundationCrawler(),
            GovUnitCrawler.get_name(): GovUnitCrawler(),
            CrawlerHK.get_name(): CrawlerHK(),
            ZhuhaiCrawler.get_name(): ZhuhaiCrawler(),
            TJCrawler.get_name(): TJCrawler(),
            XiamenCrawler.get_name(): XiamenCrawler(),
            ShanghaiCrawler.get_name(): ShanghaiCrawler(),
            GDGZCrawler.get_name(): GDGZCrawler(),
            BeiJingCrawler.get_name(): BeiJingCrawler(),
            ChengduCrawler.get_name(): ChengduCrawler(),
            GSGuangDongCrawler.get_name(): GSGuangDongCrawler(),
            GSGuiZhouCrawler.get_name(): GSGuiZhouCrawler()
        }

    # spider_name -> crawler_cls 获取所有的爬虫类
    logger.info(f'successful load crawlers {len(crawlers)}')
    for k, v in sorted(crawlers.items()):
        logger.info(f'crawler: {k} --> {type(v)}')

    # 启动线程池, 固定worker_num个任务
    with BoundedExecutor(max_workers=ap_args.worker_num, thread_name_prefix='crawler') as process_pool:
        for i in range(ap_args.worker_num):
            future = process_pool.submit(process, crawlers, crawler_register, crawler_output_queue)
            future.add_done_callback(callback_fn)
