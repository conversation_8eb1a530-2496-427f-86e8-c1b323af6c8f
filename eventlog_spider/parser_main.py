# encoding=utf8
import argparse
import json
from concurrent.futures import Future
from threading import Lock
from resx.log import setup_logger
from pydantic import ValidationError

ap = argparse.ArgumentParser(description='通用爬虫-解析程序 解析数据到MSV 并发送Kafka消息')
ap.add_argument('--worker-num', type=int, default=1, help='worker数量')
ap_args = ap.parse_args()
logger = setup_logger(use_crawler_log=True, name='eventlog-parser', debug=False)
from resx.func import get_stack_info, cur_ts_sec, get_my_ip
from resx.tools import BoundedExecutor
from resx.kafka_client import KafkaProducerClient
from resx.config import *
from resx.redis_types import RedisQueue
from resx.func import get_env
from eventlog_spider.common.eventlog import Eventlog, SpiderCode, EventlogOld, EventlogNew
from eventlog_spider.common.eventlog_unify import Eventlog as UnifyEventlog
from eventlog_spider.parser.parser import Parse<PERSON>
from eventlog_spider.common.eventlog_unify import StatusCode
from eventlog_spider.parser.parser_gs_jx import GSJXParser
from eventlog_spider.parser.parser_gs_js import GSJSParser
from eventlog_spider.parser.parser_gs_heilongjiang import GSHeiLongJiangParser
from eventlog_spider.parser.parser_gs_hainan import GSHaiNanParser
from eventlog_spider.parser.parser_gs_tianjing import TJParser
from eventlog_spider.parser.parser_gs_zhuhai import ZhuhaiParser
from eventlog_spider.parser.parser_gs_xiamen import XiamenParser
from eventlog_spider.parser.parser_gs_shanghai import ShanghaiParser
from eventlog_spider.parser.parser_gs_gdgz import GDGZParser
from eventlog_spider.parser.parser_gs_beijing import BeiJingParser
from eventlog_spider.parser.parser_gs_sichuan_chengdu import ChengduParser
from eventlog_spider.parser.parser_gs_guangdong import GSGuangDongParser
from eventlog_spider.parser.parser_gs_guizhou import GSGuiZhouParser
from eventlog_spider.parser.parser_acftu import ACFTUParser
from eventlog_spider.parser.parser_npo import ParserNpo
from eventlog_spider.parser.parser_law import ParserLaw
from eventlog_spider.parser.parser_cods import CodsParser
from eventlog_spider.parser.parser_hk import ParserHK
from eventlog_spider.parser.parser_gov_unit import GovUnitParser
from eventlog_spider.parser.parser_foundation import FoundationParser
from eventlog_spider.parser.parser_gds import GdsParser
from eventlog_spider.parser.parser_tw import TwParser
from eventlog_spider.parser.parser_enterprise_standard import EnterpriseStandardParser

writer_eventlog_old = KafkaProducerClient(kafka_topic='gsxt.data_fusion', bootstrap_servers=CFG_KAFKA_HUAWEI)
writer_eventlog_new = KafkaProducerClient(kafka_topic='octopvs_feedback_queue', bootstrap_servers=CFG_KAFKA_HUAWEI)
writer_eventlog_unify = KafkaProducerClient(bootstrap_servers='kafka.middleware.huawei:9092', kafka_topic='basic_crawler_feedback')

fs = {}
fs_lock = Lock()


def callback_fn(future: Future):
    eventlog: Eventlog = fs[future]
    with fs_lock:
        del fs[future]
    try:
        eventlog = future.result()
    except Exception as e:
        # 根据eventlog类型设置对应的错误状态
        if isinstance(eventlog, EventlogOld) and hasattr(eventlog, 'spider_code'):
            eventlog.spider_code = SpiderCode.GIVE_UP
        elif isinstance(eventlog, (EventlogNew, UnifyEventlog)) and hasattr(eventlog, 'code'):
            eventlog.code = StatusCode.GIVE_UP
        else:
            logger.warning(f"无法设置错误状态，未知的eventlog类型: {type(eventlog)}")
        logger.info(f'error process {eventlog} set error status {e} {get_stack_info()}')
    # 只有EventlogOld和EventlogNew有spider字段，UnifyEventlog没有
    if isinstance(eventlog, (EventlogOld, EventlogNew)) and hasattr(eventlog, 'spider'):
        eventlog.spider.send_ts = cur_ts_sec()
    eventlog_str = eventlog.to_json()
    # 根据eventlog类型选择对应的kafka writer
    if isinstance(eventlog, EventlogOld):
        ret = writer_eventlog_old.write(eventlog_str)
    elif isinstance(eventlog, EventlogNew):
        ret = writer_eventlog_new.write(eventlog_str)
    elif isinstance(eventlog, UnifyEventlog):
        ret = writer_eventlog_unify.write(eventlog_str)
    else:
        logger.error(f"未知的eventlog类型: {type(eventlog)}")
        ret = None
    logger.info(f'OUTPUT {eventlog_str} ret={ret}')


def eventlog(dd: dict):
    """
    尝试按顺序解析3种eventlog格式：EventlogOld -> EventlogNew -> UnifyEventlog
    """
    try:
        return EventlogOld(**dd)
    except ValidationError:
        try:
            return EventlogNew(**dd)
        except ValidationError:
            try:
                return UnifyEventlog(**dd)
            except ValidationError as e:
                logger.error(f"无法解析eventlog数据，所有格式都失败: {e}")
                return None


if __name__ == '__main__':
    if get_my_ip() == '*************':
        crawler_output_queue = RedisQueue(**CFG_REDIS_GS, name=f'eventlog_spider:crawler_output.tw', db=3)
    else:
        crawler_output_queue = RedisQueue(**CFG_REDIS_GS, name=f'eventlog_spider:crawler_output.{get_env().value}', db=3)
    # spider_name -> parser_cls 获取所有的解析类
    parsers = {
        EnterpriseStandardParser.get_name(): EnterpriseStandardParser(),
        TwParser.get_name(): TwParser(),
        GdsParser.get_name(): GdsParser(),
        ACFTUParser.get_name(): ACFTUParser(),
        ParserNpo.get_name(): ParserNpo(),
        ParserLaw.get_name(): ParserLaw(),
        GSJXParser.get_name(): GSJXParser(),
        GSJSParser.get_name(): GSJSParser(),
        GSHeiLongJiangParser.get_name(): GSHeiLongJiangParser(),
        GSHaiNanParser.get_name(): GSHaiNanParser(),
        CodsParser.get_name(): CodsParser(),
        FoundationParser.get_name(): FoundationParser(),
        ParserHK.get_name(): ParserHK(),
        GovUnitParser.get_name(): GovUnitParser(),
        TJParser.get_name(): TJParser(),
        ZhuhaiParser.get_name(): ZhuhaiParser(),
        XiamenParser.get_name(): XiamenParser(),
        ShanghaiParser.get_name(): ShanghaiParser(),
        GDGZParser.get_name(): GDGZParser(),
        BeiJingParser.get_name(): BeiJingParser(),
        ChengduParser.get_name(): ChengduParser(),
        GSGuangDongParser.get_name(): GSGuangDongParser(),
        GSGuiZhouParser.get_name(): GSGuiZhouParser()
    }
    logger.info(f'successful load parsers {len(parsers)}')
    for k, v in sorted(parsers.items()):
        logger.info(f'crawler: {k} --> {type(v)}')

    # 启动线程池
    with BoundedExecutor(max_workers=ap_args.worker_num, thread_name_prefix='parser') as process_pool:
        for s in crawler_output_queue.generate():
            try:
                d = json.loads(s)
                eventlog_: Eventlog = eventlog(d)
                if not eventlog_:
                    logger.warning(f'error from dict {s}')
                    continue
            except Exception as e_:
                logger.warning(f'error eventlog {e_}')
                continue
            # 根据eventlog类型获取parser名称
            if isinstance(eventlog_, (EventlogOld, EventlogNew)):
                parser_name = eventlog_.selector.inst_name
            elif isinstance(eventlog_, UnifyEventlog):
                parser_name = eventlog_.selector.crawler_name
            else:
                logger.error(f"未知的eventlog类型，无法获取parser名称: {type(eventlog_)}")
                continue

            if parser_name not in parsers:
                logger.warning(f"未找到对应的parser: {parser_name}")
                continue

            parser: Parser = parsers[parser_name]
            future_ = process_pool.submit(parser.parse, eventlog_)
            fs[future_] = eventlog_
            future_.add_done_callback(callback_fn)
