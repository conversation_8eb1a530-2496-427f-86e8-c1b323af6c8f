import json
import re
import time
from urllib.parse import quote

import requests
from bs4 import BeautifulSoup

from eventlog_spider.scripts.order_char.main import main

LABEL_KEYS = ["企业名称", "统一代码", "标准名称", "标准编号", "发布时间", "状态"]


def _compact_text_for_label(label: str, node) -> str:
    """
    根据字段类型决定如何拼接文本：
    - 企业名称 / 标准名称：无空格拼接（处理<b>导致的中文拆分）
    - 其他字段：用单空格拼接，保留必要空格
    """
    if not node:
        return ""
    parts = list(node.stripped_strings)
    if label in ("企业名称", "标准名称"):
        return "".join(parts)  # 中文名、标题等不应被空格打断
    return " ".join(parts)  # 编号/时间/状态等保留合理空格


def _next_value_div(label_div):
    """
    给定包含<strong>的label容器(div.col-md-1 ...)，找到紧随其后的值容器(div.col-md-5 ...)
    中间可能穿插换行/空白节点，因此循环前进直到命中。
    """
    sib = label_div.next_sibling
    while sib and (getattr(sib, "name", None) != "div" or "col-md-5" not in (sib.get("class") or [])):
        sib = sib.next_sibling
    return sib


def parse_info_list(html: str):
    soup = BeautifulSoup(html, "lxml")  # 如无lxml也可用 "html.parser"
    result = []

    for block in soup.select("div.info-list > div.info-item"):
        record = {k: "" for k in LABEL_KEYS}
        std_id = None

        # 遍历每一行；每行可能包含多对 strong/值
        for row in block.select("div.row"):
            # 找到所有 strong 标签作为“键”
            for strong in row.find_all("strong"):
                label = strong.get_text(strip=True)
                if label not in LABEL_KEYS:
                    continue

                # strong 的父节点是 label 容器(div.col-md-1 ...)，值在后一个 div.col-md-5 ...
                label_div = strong.parent
                value_div = _next_value_div(label_div)
                record[label] = _compact_text_for_label(label, value_div)

                # 可选：从值里的 <a href="javascript:toDetail('...')"> 抽取标准ID
                if value_div:
                    a = value_div.find("a", href=True)
                    if a and not std_id:
                        m = re.search(r"toDetail\('([^']+)'\)", a["href"])
                        if m:
                            std_id = m.group(1)

        # 兜底：若状态未被上述逻辑抓到，尝试直接读 badge
        if not record["状态"]:
            badge = block.select_one("span.badge")
            if badge:
                record["状态"] = badge.get_text(strip=True)

        if std_id:
            record["标准ID"] = std_id

        result.append(record)

    return result


def text_join(node):
    """把节点内可见文本用单空格拼接。"""
    if not node:
        return ""
    return " ".join(s.strip() for s in node.stripped_strings)


def parse_key_value_table(table):
    """
    解析“企业基本信息/标准信息”这类 KV 表格。
    规则：每个 label 的 <td> 带 class="td-label"，其**后一个** <td> 是值；
          兼容值单元格的 colspan（如“公开时间”、“查看文本”）。
    """
    data = {}
    for tr in table.select("tbody > tr"):
        tds = tr.find_all("td")
        i = 0
        while i < len(tds):
            td = tds[i]
            if "td-label" in (td.get("class") or []):
                label = text_join(td)
                value = ""
                if i + 1 < len(tds):
                    value_cell = tds[i + 1]
                    if label == "查看文本":
                        a_tag = value_cell.find("a", href=True)
                        if not a_tag or not a_tag.has_attr("href"):
                            return ""
                        m = re.search(r"toPdfDetail\('([^']+)'\)", a_tag["href"])
                        value = m.group(1) if m else ""
                    else:
                        value = text_join(value_cell)

                    # 跳过被合并的列（colspan），避免错位
                    span = int(value_cell.get("colspan") or 1)
                    i += span
                data[label] = value
            i += 1
    return data


def parse_products_table(container):
    """
    解析“执行该标准的产品信息”：
      - thead 的 th 作为键
      - tbody 的每一行（td/th 混用）映射为 dict
    """
    table = container.find("table")
    headers = [text_join(th) for th in table.select("thead th")]
    rows = []
    for tr in table.select("tbody tr"):
        cells = tr.find_all(["td", "th"])
        if not cells:
            continue
        row = {headers[i]: text_join(cells[i]) if i < len(cells) else "" for i in range(len(headers))}
        rows.append(row)
    return rows


def parse_detail_html(html: str):
    soup = BeautifulSoup(html, "lxml")

    # 定位三个模块块
    def find_block(title):
        for blk in soup.select("div.table-datas"):
            if text_join(blk.select_one(".heading")) == title:
                return blk
        return None

    blk_basic = find_block("企业基本信息")
    blk_std = find_block("标准信息")
    blk_prod = find_block("执行该标准的产品信息")

    result = {
        "企业基本信息": parse_key_value_table(blk_basic.find("table")) if blk_basic else {},
        "标准信息": parse_key_value_table(blk_std.find("table")) if blk_std else {},
        "执行该标准的产品信息": parse_products_table(blk_prod) if blk_prod else [],
    }
    return result


headers = {
    "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/135.0.0.0 Safari/537.36 Edg/135.0.0.0",
}
session = requests.session()

url = f"https://www.qybz.org.cn/gc/geetest/query?t={int(time.time() * 1000)}"
res = session.get(url, headers=headers)
validate, challenge = main(json.loads(res.json()))

url = "https://www.qybz.org.cn/user/searchR"
params = {
    "keyword": quote('石家庄玉吉日化有限公司'),
    "pageNo": "1",
    "geetest_challenge": challenge,
    "geetest_validate": validate,
    "geetest_seccode": f"{validate}|jordan"
}
res = session.get(url, headers=headers, params=params)
aa = parse_info_list(res.text)
print(json.dumps(aa, ensure_ascii=False, indent=2))
for item in aa:
    std_id = item.get("标准ID")
    detail_url = f"https://www.qybz.org.cn/user/detail/{std_id}"
    detail_res = session.get(detail_url, headers=headers)
    detail_data = parse_detail_html(detail_res.text)
    print(json.dumps(detail_data, ensure_ascii=False, indent=2))
    break
