import requests
from loguru import logger
import time


def click_verify(image):
    try:
        a = time.time()
        # files = {'image': ('image.png', image, 'image/png')} 10.99.202.163
        res = requests.post('http://10.99.202.163:6666/order_char', files={'image': image}, timeout=5)
        logger.info(f'识别耗时：{time.time() - a}')
        return res.json()
    except requests.exceptions.Timeout:
        logger.warning("-------------------------------------------------------------------------------\n"
                       ""
                       "验证码识别超时5s\n"
                       ""
                       "-------------------------------------------------------------------------------")
        raise Exception("验证码识别超时5s")


if __name__ == '__main__':
    with open('/Users/<USER>/Downloads/1.jpg', 'rb') as f:
        res = click_verify(f)
        print(res)
