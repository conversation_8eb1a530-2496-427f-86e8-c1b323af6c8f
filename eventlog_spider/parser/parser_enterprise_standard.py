import json
from datetime import datetime, date
from pydantic import Field, conint
from typing import Optional, Union

from resx.base_model import BaseModel
from eventlog_spider.common.eventlog_unify import Eventlog
from eventlog_spider.parser.parser import ParserTask, Parser, ParseTools
from resx.ext.mysql_rw_splitting import MySQLRWSplittingDao
from resx.config import *
from resx.log import setup_logger

logger = setup_logger(name=__name__)


class EnterpriseStandardParserTask(ParserTask):
    pass


class EnterpriseStandardParser(Parser, ParseTools):
    @classmethod
    def get_name(cls):
        return 'enterprise_standard'

    def __init__(self):
        self.dao = MySQLRWSplittingDao(
            mysql_ro=CFG_MYSQL_ZX_RDS111,
            db_tb_name='data_experience_situation.enterprise_standard',
            primary_index_fields=(['orgName'], ['sName', 'sCode']),
            entity_class=EnterpriseStandard,
            dim='企业标准信息',
            ignore_fields=['id', 'is_deleted'],
        )
        super().__init__(task_cls=EnterpriseStandardParserTask)

    def do_parse(self, *args, **kwargs):
        task: EnterpriseStandardParserTask = self.get_parser_task()
        eventlog: Eventlog = task.eventlog
        standards: list[dict] = json.loads(task.pages['standards.txt'])

        standards_ = []
        company_name = ''
        for standard in standards:
            base_info = standard['base_info.txt']
            standard_info = standard['standard.txt']
            products = standard['products.txt']
            company_name = base_info['机构名称']
            standards_.append(EnterpriseStandard(**{
                'orgName': base_info['机构名称'],
                'sName': standard_info['标准名称'],
                'sCode': standard_info['标准编号'],
                'sStatus': standard_info['状态'],
                'submitTime': standard_info['公开时间'],
                'product_information': json.dumps([self.mapping(product) for product in products], ensure_ascii=False),
                'pdf_path': standard_info['查看文本']
            }))

        self.dao.save_group(standards_, [company_name])

    @staticmethod
    def mapping(info):
        return {
            "product_name": info['产品名称'],
            "common_name": info['通用名'],
            "brand": info['品牌'],
            "bar_code": info['条码'],
            "model": info['规格'],
            "specifications": info['型号'],
            "classification": info['分类']
        }


class EnterpriseStandard(BaseModel):
    id: int = Field(default=0)
    orgName: Optional[str] = Field(default='')
    sName: Optional[str] = Field(default='')
    sCode: Optional[str] = Field(default='')
    sType: Optional[str] = Field(default='')
    sStatus: Optional[str] = Field(default='')
    submitTime: Union[datetime, str] = Field(default='')
    revocatory_date: Optional[str] = Field(default='')
    product_information: Optional[str] = Field(default='')
    pdf_path: Optional[str] = Field(default='')
    is_deleted: int = Field(default=0)
