# -*- coding: UTF-8 -*-
"""
@Title   ：黑龙江工商
<AUTHOR>
@Project ：gs_spider
@File    ：parser_heilongjiang.py
@Date    ：2025/2/8 11:14
"""
import re
from lxml import etree
from resx.func import to_date
from biz_utils.msv_write import *
from eventlog_spider.common.eventlog import EventlogOld as Eventlog
from eventlog_spider.parser.parser_gs import GSParserTask as GSXTParserTask, <PERSON><PERSON>ars<PERSON> as GSXTParser
from resx.log import setup_logger

logger = setup_logger(name=__name__)


class GSHeiLongJiangParserTask(GSXTParserTask):
    def __init__(self, eventlog: Eventlog, pages):
        self.search = eventlog.selector.get_info('keyword')
        super().__init__(eventlog, pages)


class GSHeiLongJiangParser(GSXTParser):
    def __init__(self):
        super().__init__(task_cls=GSHeiLongJiangParserTask, msv_source=MSVSource.HLJ)

    @classmethod
    def get_name(cls):
        return 'hlj'

    def do_parse(self):
        """解析企业信息，包括基本信息、注册号、股东、主要人员和变更记录，并将结果写入多源多版本记录。"""
        final_result = dict()
        task = self.get_parser_task()

        # 照面信息
        detail_html = etree.HTML(task.pages.get("detail.html"))
        basic_result = self.parser_basic_info(detail_html)
        final_result["basic_info"] = basic_result
        logger.info(f"[{task.search}]:照面信息解析完成")

        # 股东信息
        investor_html_list = [key for key in task.pages if key.startswith("investor_list")]
        for investor_html in investor_html_list:
            final_result[f"{investor_html.rstrip('.html')}"] = self.parser_investor_info(task, task.pages.get(investor_html))
        logger.info(f"[{task.search}]:股东信息解析完成")

        # 主要人员
        staff_html = task.pages.get("staff_list.html")
        final_result["staff_info"] = self.parser_staff_info(staff_html) if staff_html else ""
        logger.info(f"[{task.search}]:主要人员解析完成")

        # 变更记录
        change_html_list = [key for key in task.pages if key.startswith("change_list")]
        for change_html in change_html_list:
            final_result[f"{change_html.rstrip('.html')}"] = self.parser_change_info(task.pages.get(change_html))
        logger.info(f"[{task.search}]:变更记录解析完成")

        logger.info(final_result)
        # 照面信息记录
        msv_basic_info = self.write_msv_basic_info(final_result.get("basic_info"))
        company_type, company_legal_name = msv_basic_info.company_org_type, msv_basic_info.legal_person_name
        # 判断是不是合伙企业 且 不是分支机构
        msv_partnerships = []
        if company_type and company_legal_name and "合伙" in company_type and "分支机构" not in company_type:
            # 处理合伙企业的法人代表
            legal_names = company_legal_name.split("、") if "、" in company_legal_name else [company_legal_name]
            for person_name in legal_names:
                executive = re.sub(r"[(（]委派代表.*[）)]", "", person_name)
                represen = ""
                if "委派代表" in person_name:
                    represen = re.findall(r"委派代表[:：](.*)[）)]", person_name)
                    represen = represen[0].replace("委派代表:", "").replace("委派代表：", "") if represen else ""
                msv_partnerships.append(MSVPartnership.from_dict(dict(executive=executive, represen=represen)))
            msv_basic_info.legal_person_name = None
        task.set_base(msv_basic_info, msv_partnerships)
        logger.info(f"[{task.event_id}] 照面信息写入完成")

        # 股东信息
        investor_list = self.conformity_investor_list(final_result)
        if investor_list:
            msv_investors = self.prd_msv_investors(investor_list)
            logger.info(f"[{task.event_id}] 股东信息写入完成")
            task.set_investors(msv_investors)

        # 主要人员
        msv_staff = self.prd_msv_staff_info(final_result.get("staff_info"))
        if msv_staff and not msv_basic_info.credit_code and not msv_basic_info.credit_code.startswith("93"):
            logger.info(f"[{task.event_id}] 主要人员写入完成")
            task.set_staffs(msv_staff)

        # 变更记录
        change_list = self.conformity_change_list(final_result)
        if change_list:
            msv_change = self.prd_change_list(change_list)
            logger.info(f"[{task.event_id}] 变更信息写入完成")
            task.set_changes(msv_change)

        logger.info(f"[{task.event_id}] 解析完成")

    def parser_basic_info(self, html: etree.HTML) -> dict:
        """解析主页信息，提取基本企业信息。
        Args:
            html (str): 企业主页信息的 html。
        Returns:
            dict: 包含企业基本信息的字典。
        """
        # [企业名]
        refer_company_name = ["企业名称", "名称"]
        company_name = self.loop_match_info(html, refer_company_name)

        # [公司类型]
        refer_company_org_type = ["主体类型", "公司类型", "类型", "企业类型", "组成形式", "经济性质", "类型", "组织形式"]
        company_org_type = self.loop_match_info(html, refer_company_org_type)

        # [法人]
        refer_legal_person_name = ["法定代表人", "企业法人", "法定代表人姓名", "经营者姓名", "负责人", "负责人姓名", "投资人姓名",
                                   "首席代表人姓名", "执行事务合伙人", "经营者", "投资人", "股东", "执行合伙人", "理事长(法定代表人)",
                                   "法定代表", "首席代表", "主任姓名", "法人代表"]
        legal_person_name = self.loop_match_info(html, refer_legal_person_name, "LP")

        # [注册资本]
        refer_reg_capital = ["注册资本", "成员出资总额"]
        reg_capital = self.loop_match_info(html, refer_reg_capital)

        # [成立时间]
        refer_establish_time = ["成立日期", "注册日期"]
        establish_time = self.loop_match_info(html, refer_establish_time)

        # [统一信用代码]
        refer_credit_no = ["统一社会信用代码"]
        credit_no = self.loop_match_info(html, refer_credit_no)

        # [营业自]
        refer_from_time = ["营业期限自", "经营期限自", "合伙期限自", "成立日期"]
        from_time = self.loop_match_info(html, refer_from_time)

        # [营业至]
        refer_to_time = ["营业期限至", "经营期限至", "合伙期限至"]
        to_time = self.loop_match_info(html, refer_to_time)

        # [注册机关]
        refer_reg_institute = ["登记机关"]
        reg_institute = self.loop_match_info(html, refer_reg_institute)

        # [核准日期]
        refer_approved_time = ["核准日期"]
        approved_time = self.loop_match_info(html, refer_approved_time)

        # [住所]
        refer_reg_location = ["住所", "营业场所", "主要经营场所", "经营场所"]
        reg_location = self.loop_match_info(html, refer_reg_location)

        # [企业状态]
        refer_reg_status = ["登记状态"]
        reg_status = self.loop_match_info(html, refer_reg_status)

        # [经营范围]
        refer_business_scope = ["经营范围", "业务范围"]
        business_scope = self.loop_match_info(html, refer_business_scope)

        # [注册号]
        refer_reg_number = ["注册号"]
        reg_number = self.loop_match_info(html, refer_reg_number)

        # [经营场所]
        refer_business_location = ['经营场所']
        business_location = self.loop_match_info(html, refer_business_location)
        if reg_location and business_location and reg_location != business_location:
            logger.warning(f"[{company_name}|{credit_no}]:住所和经营场所不一致")

        # 封装字典
        basic_info = dict()
        basic_info["company_name"] = company_name
        basic_info["legal_person_name"] = legal_person_name
        basic_info["company_org_type"] = company_org_type
        basic_info["reg_location"] = reg_location
        basic_info["establish_time"] = establish_time
        basic_info["from_time"] = from_time
        basic_info["to_time"] = to_time
        basic_info["business_scope"] = business_scope
        basic_info["reg_status"] = reg_status
        basic_info["reg_capital"] = reg_capital
        basic_info["credit_code"] = credit_no
        basic_info["approved_time"] = approved_time
        basic_info["reg_institute"] = reg_institute
        basic_info["reg_number"] = reg_number
        return basic_info

    def parser_investor_info(self, task, investor_html):
        final_value = dict()
        investor_html = etree.HTML(investor_html)
        for investor in investor_html.xpath("//table[2]//tr"):
            # 股东序号
            temple_investor_number = investor.xpath(".//td[1]//text()")
            investor_number = self.loop_replace_space(temple_investor_number[0]) if temple_investor_number else ""

            # 股东名称
            temple_investor_name = investor.xpath(".//td[2]//text()")
            investor_name = self.loop_replace_space(temple_investor_name[0], "LP") if temple_investor_name else ""

            # 股东类型
            temple_investor_type = investor.xpath(".//td[3]//text()")
            investor_type = self.loop_replace_space(temple_investor_type[0]) if temple_investor_type else ""

            # 证件类型
            temple_investor_card_type = investor.xpath(".//td[4]//text()")
            investor_card_type = self.loop_replace_space(temple_investor_card_type[0]) if temple_investor_card_type else ""

            # 证件号码
            temple_investor_card_num = investor.xpath(".//td[5]//text()")
            investor_card_num = self.loop_replace_space(temple_investor_card_num[0]) if temple_investor_card_num else ""

            # 股东ID
            temple_onclick_label = investor.xpath(".//td[6]/a/@onclick")
            investor_id = re.findall(r"seeInvest\('(.*?)'", self.loop_replace_space(temple_onclick_label[0]))[0] if temple_onclick_label else ""

            # 股东详细信息
            investor_detail_html = task.pages.get(f"investor_info_{investor_id}.html")
            investor_detail = self.parser_investor_detail(investor_detail_html) if investor_detail_html else ""

            final_value[f"investor_{investor_number}"] = {}
            final_value[f"investor_{investor_number}"]["number"] = investor_number if investor_number else ""
            final_value[f"investor_{investor_number}"]["investor_name"] = investor_name if investor_name else ""
            final_value[f"investor_{investor_number}"]["investor_type"] = investor_type if investor_type else ""
            final_value[f"investor_{investor_number}"]["card_type"] = investor_card_type if investor_card_type else ""
            final_value[f"investor_{investor_number}"]["card_num"] = investor_card_num if investor_card_num else ""
            final_value[f"investor_{investor_number}"]["investor_detail"] = investor_detail if investor_detail else ""
        return final_value

    def parser_staff_info(self, html: str) -> dict:
        final_value = dict()
        staff_html = etree.HTML(html)
        for index, person_item in enumerate(staff_html.xpath(".//div[@class='keyPerInfo']")):
            # 人员名称
            # 存在 title名全 text名不全的情况 同海南
            person_name_for_title = self.loop_replace_space(person_item.xpath(".//p[1]/span/@title")[0], "LP") if person_item.xpath(
                ".//p[1]/span/@title") else ""
            if not person_name_for_title:
                person_name = self.loop_replace_space(person_item.xpath(".//p[1]/span/text()")[0], "LP") if person_item.xpath(".//p[1]/span/text()") else ""
            else:
                person_name = person_name_for_title

            # 人员位置
            person_position = self.loop_replace_space(person_item.xpath(".//p[2]/span/text()")[0]) if person_item.xpath(".//p[2]/span/text()") else ""

            final_value[f"staff_{index}"] = dict()
            final_value[f"staff_{index}"]["number"] = index
            final_value[f"staff_{index}"]["name"] = person_name
            final_value[f"staff_{index}"]["position"] = person_position
        return final_value

    def parser_change_info(self, html: str) -> dict:
        final_value = dict()
        change_html = etree.HTML(html)
        for tr in change_html.xpath("//div[@id='altDiv']//tr"):
            # 人员序号
            change_number = self.loop_replace_space(tr.xpath(".//td[1]/text()")[0]) if tr.xpath(".//td[1]/text()") else ""
            # 变更事项
            change_event = self.loop_replace_space(tr.xpath(".//td[2]/text()")[0], "Strip") if tr.xpath(".//td[2]/text()") else ""
            # 变更前内容
            change_before = self.loop_replace_space(tr.xpath(".//td[3]/text()")[0], "Strip") if tr.xpath(".//td[3]/text()") else ""
            # 变更后内容
            change_after = self.loop_replace_space(tr.xpath(".//td[4]/text()")[0], "Strip") if tr.xpath(".//td[4]/text()") else ""
            # 变更日期
            change_time = self.loop_replace_space(tr.xpath(".//td[5]/text()")[0]) if tr.xpath(".//td[5]/text()") else ""

            final_value[f"change_{change_number}"] = {}
            final_value[f"change_{change_number}"]["number"] = change_number
            final_value[f"change_{change_number}"]["change_event"] = change_event
            final_value[f"change_{change_number}"]["change_before"] = change_before
            final_value[f"change_{change_number}"]["change_after"] = change_after
            final_value[f"change_{change_number}"]["change_time"] = change_time
        return final_value

    def parser_investor_detail(self, investor_detail_html: str) -> dict:
        final_value = dict()
        investor_detail_html = etree.HTML(investor_detail_html)
        tables = investor_detail_html.xpath("//table")
        for index, table in enumerate(tables):
            if index == 0:
                # 股东投资信息
                investor_name = self.loop_replace_space(table.xpath("./tr[1]/td/text()")[0], "LP") if table.xpath("./tr[1]/td/text()") else ""
                capital = self.loop_replace_space(table.xpath("./tr[2]/td/text()")[0]) if table.xpath("./tr[2]/td/text()") else ""
                capital_actl = self.loop_replace_space(table.xpath("./tr[3]/td/text()")[0]) if table.xpath("./tr[3]/td/text()") else ""
                final_value["inv_info"] = {}
                final_value["inv_info"]["investor_name"] = investor_name
                final_value["inv_info"]["capital"] = capital
                final_value["inv_info"]["capital_actl"] = capital_actl
            elif index == 1:
                # 认缴明细
                trs = table.xpath(".//tr")
                for capital_index, tr in enumerate(trs[1:]):
                    capital_type = self.loop_replace_space(tr.xpath("./td[1]/text()")[0]) if tr.xpath("./td[1]/text()") else ""
                    capital_amonom = self.loop_replace_space(tr.xpath("./td[2]/text()")[0]) if tr.xpath("./td[2]/text()") else ""
                    capital_time = self.loop_replace_space(tr.xpath("./td[3]/text()")[0]) if tr.xpath("./td[3]/text()") else ""
                    final_value["capital_info"] = {}
                    final_value["capital_info"][f"capital_info_{capital_index}"] = {}
                    final_value["capital_info"][f"capital_info_{capital_index}"]["capital_type"] = capital_type
                    final_value["capital_info"][f"capital_info_{capital_index}"]["capital_amonom"] = capital_amonom
                    final_value["capital_info"][f"capital_info_{capital_index}"]["capital_time"] = capital_time
            elif index == 2:
                # 实缴明细
                trs = table.xpath(".//tr")
                for actl_index, tr in enumerate(trs[1:]):
                    capital_actl_type = self.loop_replace_space(tr.xpath("./td[1]/text()")[0]) if tr.xpath("./td[1]/text()") else ""
                    capital_actl_amonom = self.loop_replace_space(tr.xpath("./td[2]/text()")[0]) if tr.xpath("./td[2]/text()") else ""
                    capital_actl_time = self.loop_replace_space(tr.xpath("./td[3]/text()")[0]) if tr.xpath("./td[3]/text()") else ""
                    final_value["capital_actl_info"] = {}
                    final_value["capital_actl_info"][f"capital_actl_info_{actl_index}"] = {}
                    final_value["capital_actl_info"][f"capital_actl_info_{actl_index}"]["capital_actl_type"] = capital_actl_type
                    final_value["capital_actl_info"][f"capital_actl_info_{actl_index}"]["capital_actl_amonom"] = capital_actl_amonom
                    final_value["capital_actl_info"][f"capital_actl_info_{actl_index}"]["capital_actl_time"] = capital_actl_time
        return final_value

    @staticmethod
    def write_msv_basic_info(basic_info: dict):
        if basic_info:
            msv_base_info_data = dict(
                name=basic_info.get("company_name"),
                legal_person_name=basic_info.get("legal_person_name"),
                company_org_type=basic_info.get("company_org_type"),
                reg_location=basic_info.get("reg_location"),
                estiblish_time=to_date(basic_info.get("establish_time")),
                from_time=to_date(basic_info.get("from_time")),
                to_time=to_date(basic_info.get("to_time")) if basic_info.get("to_time").strip() != "长期" else None,
                business_scope=basic_info.get("business_scope").strip(),
                reg_status=basic_info.get('reg_status', ""),
                reg_capital=basic_info.get('reg_capital', ""),
                credit_code=basic_info.get('credit_code', ""),
                reg_number=basic_info.get('reg_number', ""),
                approved_time=to_date(basic_info.get('approved_time', "")),
                reg_institute=basic_info.get('reg_institute', ""),
            )
            msv_base_info = MSVBaseInfo.from_dict(msv_base_info_data)
            return msv_base_info

    @staticmethod
    def prd_msv_investors(investor_list: list):
        msv_investor_set, msv_investor_list = set(), list()
        for investor in investor_list:
            investor_capital_list, investor_capital_actl_list = list(), list()
            capital_list = investor.get("investor_detail").get("capital_info") if investor.get("investor_detail") else ""
            if capital_list:
                for capital in capital_list.values():
                    temple = dict()
                    temple["amomon"] = capital.get("capital_amonom")
                    temple["paymet"] = capital.get("capital_type")
                    temple["time"] = to_date(capital.get("capital_time")) if capital.get("capital_time") else ""
                    investor_capital_list.append(temple)

            capital_actl_list = investor.get("investor_detail").get("capital_actl_info") if investor.get("investor_detail") else ""
            if capital_actl_list:
                for capital in capital_actl_list.values():
                    temple = dict()
                    temple["amomon"] = capital.get("capital_actl_amonom")
                    temple["paymet"] = capital.get("capital_actl_type")
                    temple["time"] = to_date(capital.get("capital_actl_time")) if capital.get("capital_actl_time") else ""
                    investor_capital_actl_list.append(temple)

            final_value = {
                "investor_name": investor["investor_name"],
                "investor_type": 0,
                "capital": investor_capital_list,
                "capital_actl": investor_capital_actl_list,
                "detail": {
                    "businessLicNo": investor.get("card_num"),
                    "businessLicType": investor.get("card_type"),
                    "shareholderName": investor.get("investor_name"),
                    "shareholderType": investor.get("investor_type")
                }
            }
            if not final_value["detail"]["businessLicNo"]:
                del final_value["detail"]["businessLicNo"]
            if not final_value["detail"]["businessLicType"]:
                del final_value["detail"]["businessLicType"]
            if not final_value["detail"]["shareholderName"]:
                del final_value["detail"]["shareholderName"]
            if not final_value["detail"]["shareholderType"]:
                del final_value["detail"]["shareholderType"]

            msv_investor_set.add(str(final_value))
            msv_investor_list.append(MSVInvestor.from_dict(final_value))

            if len(msv_investor_set) != len(msv_investor_list):
                logger.warning("农合社-主要人员转股东:存在重复")
                msv_investor_list.remove(MSVInvestor.from_dict(final_value))
        return msv_investor_list

    @staticmethod
    def prd_msv_staff_info(staff_info: dict):
        if staff_info:
            staff_set = set()
            staff_info_list = staff_info.values()
            sorted_staff_info_list = sorted(staff_info_list, key=lambda x: int(x['number']))
            msv_staff = []
            for staff in sorted_staff_info_list:
                staff_set.add(json.dumps({
                    'staff_name': staff['name'],
                    'staff_position': staff['position']
                }, ensure_ascii=False))
                msv_staff.append(MSVStaff.from_dict({
                    'staff_name': staff['name'],
                    'staff_position': staff['position']
                }))
                if len(staff_set) != len(msv_staff):
                    msv_staff.remove(MSVStaff.from_dict({
                        'staff_name': staff['name'],
                        'staff_position': staff['position']
                    }))
                    logger.warning("主要人员存在重复")
            return msv_staff
        return ""

    @staticmethod
    def prd_change_list(change_info):
        if change_info:
            msv_change_list = []
            for change_item in change_info:
                msv_change_list.append(MSVChange.from_dict(
                    {
                        'change_item': change_item['change_event'],
                        'change_time': to_date(change_item['change_time']) if change_item["change_time"] else "",
                        'content_before': change_item['change_before'],
                        'content_after': change_item['change_after']
                    }))
            return msv_change_list
        return ""

    @staticmethod
    def loop_replace_space(string: str, target: str = "") -> str:
        for _ in range(5):
            string = string.replace("  ", " ")
        if target == "LP":
            string = string.replace("(", "（").replace(")", "）")
            string = re.sub(r'\s+', '', string) if not re.search(r'[a-zA-Z]', string) else string.strip()
        elif target == "Strip":
            string = string.strip()
        else:
            string = re.sub(r'\s+', '', string)
        return string

    @staticmethod
    def conformity_investor_list(data):
        investor_list = [key for key in data if key.startswith("investor_list")]
        investors_list = []
        for investorsNum in investor_list:
            for investor_list in data[investorsNum].values():
                investors_list.append(investor_list)
        sorted_investors_list = sorted(investors_list, key=lambda x: int(x['number']))
        return sorted_investors_list

    @staticmethod
    def conformity_change_list(data):
        change_list = [key for key in data if key.startswith("change_list")]
        changeinfo_list = []
        for changeNum in change_list:
            for change_list in data[changeNum].values():
                changeinfo_list.append(change_list)
        sorted_change_list = sorted(changeinfo_list, key=lambda x: int(x['number']))
        return sorted_change_list

    def loop_match_info(self, html: etree.HTML, infos: list, target: str = "") -> str:
        for info in infos:
            temple = html.xpath(f"//table[@class='xinxi']//*[contains(text(), '{info}')]/span/text()")
            value = self.loop_replace_space(temple[0], target) if temple else ""
            if value:
                return value

            if info == "注册号" and not value:
                task = self.get_parser_task()
                detail2_html = task.pages["detail2.html"]
                html = etree.HTML(detail2_html)
                temple = html.xpath(f"//input[@id='regNo']/@value")
                value = self.loop_replace_space(temple[0], target) if temple else ""
                if value:
                    return value
        return ""
