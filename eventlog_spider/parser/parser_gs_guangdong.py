# -*- coding: UTF-8 -*-
"""
@Title   ：广东工商
<AUTHOR>
@Project ：gs_spider
@File    ：parser_gs_guangdong.py
@Date    ：2025/2/20 15:18 
"""
import re
from datetime import datetime, time
from lxml import etree
from resx.func import to_date, to_datetime
from biz_utils.msv_write import *
from eventlog_spider.common.eventlog import EventlogOld as Eventlog
from eventlog_spider.parser.parser_gs import GSParserTask as GSXTParserTask, <PERSON><PERSON>ars<PERSON> as GSXTParser
from resx.log import setup_logger
from biz_utils.entity.gs_others.company_abnormal_info import CompanyAbnormalInfoDao

logger = setup_logger(name=__name__)


class GSGuangDongParserTask(GSXTParserTask):
    def __init__(self, eventlog: Eventlog, pages):
        self.search = eventlog.selector.get_info('keyword')
        self.company_id = eventlog.selector.word
        super().__init__(eventlog, pages)


class GSGuangDongParser(GSXTParser):
    def __init__(self):
        self.abnormal_dao = CompanyAbnormalInfoDao()
        super().__init__(task_cls=GSGuangDongParserTask, msv_source=MSVSource.GD)

    @classmethod
    def get_name(cls):
        return 'gd'

    def do_parse(self):
        final_result = dict()
        task = self.get_parser_task()
        company_id = task.company_id
        # 照面信息
        final_result["basic_info"] = self.parser_basic_info(task.pages.get("basic_info.json"))
        logger.info(f"[{task.search}]: 照面信息解析完成")

        # 股东信息
        final_result[f"investor_info"] = self.parser_investor_info(task, task.pages.get("investor_list_1.json"))
        logger.info(f"[{task.search}]: 股东信息解析完成")

        # 主要人员信息
        staff_list = [key for key in task.pages if key.startswith("staff_list")]
        for staff_ in staff_list:
            final_result[f"{staff_.rstrip('.json')}"] = self.parser_staff_info(task.pages.get(f"{staff_}"))
        logger.info(f"[{task.search}]: 主要人员信息解析完成")

        # 变更记录信息
        change_list = [key for key in task.pages if key.startswith("change_list")]
        for change_ in change_list:
            final_result[f"{change_.strip('.json')}"] = self.parser_change_info(task.pages.get(f"{change_}"))
        logger.info(f"[{task.search}]: 变更记录信息解析完成")

        # 经营异常
        abnormal_list = [key for key in task.pages if key.startswith("abnormal_list")]
        for abnormal_ in abnormal_list:
            final_result[f"{abnormal_.rstrip('.json')}"] = self.parser_abnormal_info(task.pages.get(f"{abnormal_}"))

        # 入库
        # 照面信息
        msv_basic_info = self.write_msv_basic_info(final_result.get("basic_info"))
        company_type, company_legal_name = msv_basic_info.company_org_type, msv_basic_info.legal_person_name
        # 判断是不是合伙企业 且 不是分支机构
        msv_partnerships = []
        if company_type and company_legal_name and "合伙" in company_type and "分支机构" not in company_type:
            # 处理合伙企业的法人代表
            if company_legal_name:
                legal_names = company_legal_name.split("、") if "、" in company_legal_name else [company_legal_name]
            else:
                legal_names = []
            for person_name in legal_names:
                executive = re.sub(r"[(（]委派代表.*[）)]", "", person_name)
                represen = ""
                if "委派代表" in person_name:
                    represen = re.findall(r"委派代表[:：](.*)[）)]", person_name)
                    represen = represen[0].replace("委派代表:", "").replace("委派代表：", "") if represen else ""
                msv_partnerships.append(MSVPartnership.from_dict(dict(executive=executive, represen=represen)))
            msv_basic_info.legal_person_name = None
        task.set_base(msv_basic_info, msv_partnerships)

        # 股东信息
        investor_list = self.conformity_investor_list(final_result)
        if investor_list:
            msv_investors = self.prd_msv_investors(investor_list)
            logger.info(f"[{task.event_id}] 股东信息写入完成")
            task.set_investors(msv_investors)

        # 主要人员
        staff_list = self.conformity_staff_list(final_result)
        msv_staff = self.prd_msv_staff_info(staff_list)
        if msv_staff and not msv_basic_info.credit_code.startswith("93"):
            logger.info(msv_staff)
            logger.info(f"[{task.event_id}] 主要人员写入完成")
            task.set_staffs(msv_staff)
        # else:
        #     if not investor_list:
        #         msv_investors = self.prd_msv_investors(staff_list)
        #         logger.info(msv_investors)

        # 变更记录
        change_list = self.conformity_change_list(final_result)
        if change_list:
            msv_change = self.prd_change_list(change_list)
            logger.info(change_list)
            logger.info(f"[{task.event_id}] 变更信息写入完成")
            task.set_changes(msv_change)

        # 经营异常
        abnormal_list = self.conformity_abnormal_list(final_result)
        if abnormal_list:
            msv_abnormal_result = self.write_msv_abnormal_info(abnormal_list)
            task.set_abnormals(msv_abnormal_result)
            # self.rem_abnormal_for_mysql(company_id, abnormal_list)
            logger.info(f"[{task.event_id}] 经营异常写入完成")
        logger.info(final_result)

    @staticmethod
    def parser_basic_info(text: str):
        basic_json = json.loads(text.strip())
        basic_data = basic_json.get("data", {})
        basic_info = dict()
        # 统一社会信用代码
        basic_info["credit_code"] = basic_data["uniscid"] if basic_data.get("uniscid") else ""
        # 企业名称
        basic_info["company_name"] = basic_data["entname"] if basic_data.get("entname") else ""
        if not basic_info["company_name"]:
            basic_info["company_name"] = basic_data["traname"] if basic_data.get("traname") else ""
        # 注册号
        basic_info["reg_number"] = basic_data["regno"] if basic_data.get("regno") else ""
        # 法人代表
        basic_info["legal_person_name"] = basic_data["name"] if basic_data.get("name") else ""
        # 公司类型
        basic_info["company_org_type"] = basic_data["enttypeCn"] if basic_data.get("enttypeCn") else ""
        # 成立时间
        basic_info["establish_time"] = basic_data["estdate"] if basic_data.get("estdate") else ""
        # 注册资本
        basic_info["reg_capital"] = basic_data["regcap"] if basic_data.get("regcap") else ""
        # 核准日期
        basic_info["approved_time"] = basic_data["apprdate"] if basic_data.get("apprdate") else ""
        # 营业期限自
        basic_info["from_time"] = basic_data["opfrom"] if basic_data.get("opfrom") else ""
        # 营业期限至
        basic_info["to_time"] = basic_data["opto"] if basic_data.get("opto") else ""
        # 登记机关
        basic_info["reg_institute"] = basic_data["regorgCn"] if basic_data.get("regorgCn") else ""
        # 登记状态
        basic_info["reg_status"] = basic_data["regstateCn"] if basic_data.get("regstateCn") else ""
        # 住所
        basic_info["reg_location"] = basic_data["dom"] if basic_data.get("dom") else ""
        # 经营范围
        basic_info["business_scope"] = basic_data["opscope"] if basic_data.get("opscope") else ""

        return basic_info

    def parser_investor_info(self, task, text: str):
        if text:
            investors_dict = dict()
            investors_json = json.loads(text)
            investors_data = investors_json.get("data")
            if investors_data:
                for index, investor in enumerate(investors_data):
                    temple_ = dict()
                    # 股东名称
                    temple_["investor_name"] = self.loop_replace_space(investor["invName"], "LP") if investor.get("invName") else ""
                    # 股东类型
                    temple_["investor_type"] = investor["invTypeCn"] if investor.get("invTypeCn") else ""
                    # 证件类型
                    temple_["card_type"] = investor["cerTypeCn"] if investor.get("cerTypeCn") else ""
                    # 证件号码
                    temple_["card_num"] = investor["cerNo"] if investor.get("cerNo") else ""
                    # 股东详细信息
                    temple_["investor_detail"] = self.parser_investor_details(task.pages.get(f"investor_{investor.get('invId')}.json"))

                    investors_dict[f"investor_{index+1}"] = temple_
            return investors_dict
        else:
            return {}

    def parser_investor_details(self, text):
        investor_detail = json.loads(text)
        investor_detail_data = investor_detail.get("data")
        temple_ = dict()
        # 股东投资
        temple_["inv_info"] = dict()
        temple_["inv_info"]["investor_name"] = self.loop_replace_space(investor_detail_data["invName"], "LP") if investor_detail_data.get("invName") else ""
        temple_["inv_info"]["capital"] = [{"amonom": investor_detail_data["subconam"]}] if investor_detail_data.get("subconam") else []
        temple_["inv_info"]["capital_actl"] = [{"amonom": investor_detail_data["acconam"]}] if investor_detail_data.get("acconam") else []

        # 认缴明细
        if investor_detail_data.get("proList"):
            logger.warning(f"has proList, {investor_detail_data.get('proList')}")
        temple_["capital_info"] = temple_["inv_info"]["capital"]

        # 实缴明细
        if investor_detail_data.get("actList"):
            logger.warning(f"has actList, {investor_detail_data.get('actList')}")
        temple_["capital_actl_info"] = temple_["inv_info"]["capital_actl"]

        return temple_

    @staticmethod
    def parser_staff_info(text):
        if text:
            final_staff = dict()
            staff_info = json.loads(text).get("data", {}).get("records")
            for index, staff in enumerate(staff_info):
                final_staff[f"staff_{index+1}"] = dict()
                final_staff[f"staff_{index+1}"]["id"] = staff["personId"]
                final_staff[f"staff_{index+1}"]["name"] = staff["name"]
                final_staff[f"staff_{index+1}"]["position"] = staff["position"]
            return final_staff
        else:
            return {}

    def parser_change_info(self, text):
        if text:
            final_change = dict()
            change_info = json.loads(text).get("data", {}).get("records")
            for index, change_ in enumerate(change_info):
                # 人员序号
                change_number = self.loop_replace_space(change_["pripid"])
                # 变更事项
                change_event = self.loop_replace_space(change_["alterItem"], "Strip")
                # 变更前内容
                change_before = self.loop_replace_space(change_["alterBefore"], "Strip")
                # 变更后内容
                change_after = self.loop_replace_space(change_["alterAfter"], "Strip")
                # 变更日期
                change_time = self.loop_replace_space(change_["alterDate"], "Strip")

                final_change[f"change_{index+1}"] = dict()
                final_change[f"change_{index+1}"]["change_number"] = change_number
                final_change[f"change_{index+1}"]["change_event"] = change_event
                final_change[f"change_{index+1}"]["change_before"] = change_before
                final_change[f"change_{index+1}"]["change_after"] = change_after
                final_change[f"change_{index+1}"]["change_time"] = change_time
            return final_change
        else:
            return {}

    def parser_abnormal_info(self, text):
        if text:
            final_abnormal = dict()
            abnormal_info = json.loads(text).get("data", {}).get("records")
            for index, abnormal_ in enumerate(abnormal_info):
                # 列入原因
                put_reason = abnormal_["specauseCn"]
                # 列入时间
                put_date = abnormal_["abntime"]
                # 列入机关
                put_department = abnormal_["decorgCn"]
                # 移除原因
                rem_reason = abnormal_["remexcpresCn"]
                # 移除时间
                rem_date = abnormal_["remdate"]
                # 移除机关
                rem_department = abnormal_["redecorgCn"]

                final_abnormal[f"abnormal_{index+1}"] = dict()
                final_abnormal[f"abnormal_{index+1}"]["put_reason"] = put_reason
                final_abnormal[f"abnormal_{index+1}"]["put_date"] = put_date
                final_abnormal[f"abnormal_{index+1}"]["put_department"] = put_department
                final_abnormal[f"abnormal_{index+1}"]["rem_reason"] = rem_reason
                final_abnormal[f"abnormal_{index+1}"]["rem_date"] = rem_date
                final_abnormal[f"abnormal_{index+1}"]["rem_department"] = rem_department
                final_abnormal[f"abnormal_{index+1}"]["put_reason_type"] = ""
                final_abnormal[f"abnormal_{index+1}"]["rem_reason_type"] = ""
            return final_abnormal
        else:
            return {}

    @staticmethod
    def loop_replace_space(string: str, target: str = "") -> str:
        for _ in range(5):
            string = string.replace("  ", " ")
        if target == "LP":
            string = string.replace("(", "（").replace(")", "）")
            string = re.sub(r'\s+', '', string) if not re.search(r'[a-zA-Z]', string) else string.strip()
        elif target == "Strip":
            string = string.strip()
        else:
            string = re.sub(r'\s+', '', string)
        return string

    @staticmethod
    def write_msv_basic_info(basic_info: dict):
        if basic_info:
            msv_base_info_data = dict(
                name=basic_info.get("company_name"),
                legal_person_name=basic_info.get("legal_person_name"),
                company_org_type=basic_info.get("company_org_type"),
                reg_location=basic_info.get("reg_location"),
                estiblish_time=to_date(basic_info.get("establish_time")),
                from_time=to_date(basic_info.get("from_time")),
                to_time=to_date(basic_info.get("to_time")) if basic_info.get("to_time").strip() != "长期" else None,
                business_scope=basic_info.get("business_scope").strip(),
                reg_status=basic_info.get('reg_status', ""),
                reg_capital=basic_info.get('reg_capital', ""),
                credit_code=basic_info.get('credit_code', ""),
                reg_number=basic_info.get('reg_number', ""),
                approved_time=to_date(basic_info.get('approved_time', "")),
                reg_institute=basic_info.get('reg_institute', ""),
            )
            msv_base_info = MSVBaseInfo.from_dict(msv_base_info_data)
            return msv_base_info

    @staticmethod
    def conformity_investor_list(data):
        investor_list = [key for key in data if key.startswith("investor_info")]
        investors_list = []
        for investorsNum in investor_list:
            for investor_list in data[investorsNum].values():
                investors_list.append(investor_list)
        return investors_list

    @staticmethod
    def conformity_staff_list(data):
        staff_list = [key for key in data if key.startswith("staff_list")]
        staffs_list = []
        for staff_num in staff_list:
            for staff_ in data[staff_num].values():
                staffs_list.append(staff_)
        return staffs_list

    @staticmethod
    def conformity_change_list(data):
        change_list = [key for key in data if key.startswith("change_list")]
        changes_list = []
        for change_num in change_list:
            for change_ in data[change_num].values():
                changes_list.append(change_)
        return changes_list

    @staticmethod
    def conformity_abnormal_list(data):
        abnormal_list = [key for key in data if key.startswith("abnormal_list")]
        abnormals_list = []
        for abnormal_num in abnormal_list:
            for abnormal_ in data[abnormal_num].values():
                abnormals_list.append(abnormal_)
        return abnormals_list

    @staticmethod
    def prd_msv_investors(investor_list: list):
        msv_investor_set, msv_investor_list = set(), list()
        for investor in investor_list:
            investor_capital_list, investor_capital_actl_list = list(), list()
            capital_list = investor.get("investor_detail").get("capital_info") if investor.get("investor_detail") else ""
            if capital_list:
                for capital in capital_list:
                    temple = dict()
                    temple["amomon"] = capital.get("amonom")
                    temple["paymet"] = capital.get("paymet", "")
                    temple["time"] = to_date(capital.get("time")) if capital.get("time") else ""
                    investor_capital_list.append(temple)

            capital_actl_list = investor.get("investor_detail").get("capital_actl_info") if investor.get("investor_detail") else ""
            if capital_actl_list:
                for capital in capital_actl_list:
                    temple = dict()
                    temple["amomon"] = capital.get("amonom")
                    temple["paymet"] = capital.get("paymet", "")
                    temple["time"] = to_date(capital.get("time")) if capital.get("time") else ""
                    investor_capital_actl_list.append(temple)

            investor_name = investor.get("investor_name")
            # if not investor_name:
            #     investor_name = investor.get("name")
            final_value = {
                "investor_name": investor_name,
                "investor_type": 0,
                "capital": investor_capital_list,
                "capital_actl": investor_capital_actl_list,
                "detail": {
                    "businessLicNo": investor.get("card_num"),
                    "businessLicType": investor.get("card_type"),
                    "shareholderName": investor.get("investor_name"),
                    "shareholderType": investor.get("investor_type")
                }
            }
            if not final_value["detail"]["businessLicNo"]:
                del final_value["detail"]["businessLicNo"]
            if not final_value["detail"]["businessLicType"]:
                del final_value["detail"]["businessLicType"]
            if not final_value["detail"]["shareholderName"]:
                del final_value["detail"]["shareholderName"]
            if not final_value["detail"]["shareholderType"]:
                del final_value["detail"]["shareholderType"]

            msv_investor_set.add(str(final_value))
            msv_investor_list.append(MSVInvestor.from_dict(final_value))

            if len(msv_investor_set) != len(msv_investor_list):
                logger.warning("农合社-主要人员转股东:存在重复")
                msv_investor_list.remove(MSVInvestor.from_dict(final_value))
        return msv_investor_list

    @staticmethod
    def prd_msv_staff_info(staff_info: list):
        staff_set = set()
        msv_staff = []
        if staff_info:
            for staff_ in staff_info:
                staff_set.add(json.dumps({
                    'staff_name': staff_['name'],
                    'staff_position': staff_['position']
                }, ensure_ascii=False))
                msv_staff.append(MSVStaff.from_dict({
                    'staff_name': staff_['name'],
                    'staff_position': staff_['position']
                }))
                if len(staff_set) != len(msv_staff):
                    msv_staff.remove(MSVStaff.from_dict({
                        'staff_name': staff_['name'],
                        'staff_position': staff_['position']
                    }))
                    logger.warning("主要人员存在重复")
            return msv_staff
        return ""

    @staticmethod
    def prd_change_list(change_info):
        if change_info:
            msv_change_list = []
            for change_item in change_info:
                msv_change_list.append(MSVChange.from_dict(
                    {
                        'change_item': change_item['change_event'],
                        'change_time': to_date(change_item['change_time']) if change_item["change_time"] else "",
                        'content_before': change_item['change_before'],
                        'content_after': change_item['change_after']
                    }))
            return msv_change_list
        return ""

    @staticmethod
    def write_msv_abnormal_info(abnormal_list: list) -> list:
        msv_abnormal_list = list()
        for abnormal_info in abnormal_list:
            msv_abnormal_list.append(MSVCompanyAbnormal.from_dict(
                {
                    'put_date': to_date(abnormal_info['put_date']),
                    'put_reason': abnormal_info['put_reason'],
                    'put_department': abnormal_info['put_department'],
                    'remove_date': to_date(abnormal_info['rem_date']),
                    'remove_reason': abnormal_info['rem_reason'],
                    'remove_department': abnormal_info['rem_department'],
                    'statistics_put_reason': abnormal_info['put_reason_type'],
                    'statistics_remove_reason': abnormal_info['rem_reason_type']
                }
            ))
        return msv_abnormal_list

    # def rem_abnormal_for_mysql(self, company_id, abnormal_info):
    #     abnormal_list_from_mysql = list(self.abnormal_dao.get_many(company_id=company_id))
    #     for abnormal_item in abnormal_list_from_mysql:
    #         abnormal_json = abnormal_item.to_dict()
    #
    #         id = abnormal_json.get("id")
    #         put_reason = abnormal_json.get("put_reason")
    #         put_date = abnormal_json.get("put_date")
    #         put_department = abnormal_json.get("put_department")
    #         deleted = abnormal_json.get("deleted")
    #
    #         for new_abnormal_item in abnormal_info:
    #             put_date = put_date.replace("T", " ") if "T" in put_date else put_date
    #             new_abnormal_item["put_date"] = new_abnormal_item.get("put_date").replace("T", " ") if "T" in new_abnormal_item.get("put_date") else new_abnormal_item.get("put_date")
    #             # 匹配到了 deleted = 0 的 经营异常
    #             if (new_abnormal_item.get("put_reason").startswith(put_reason) or put_reason.startswith(new_abnormal_item.get("put_reason"))) \
    #                 and (new_abnormal_item.get("put_department").startswith(put_department) or put_department.startswith(new_abnormal_item.get("put_department"))) \
    #                 and (new_abnormal_item.get("put_date").startswith(put_date) or put_date.startswith(new_abnormal_item.get("put_date"))) \
    #                 and deleted in (0, "0"):
    #                 # 如果存在 任意一个 remove 项 就进行更新
    #                 if new_abnormal_item.get("rem_reason") or new_abnormal_item.get("rem_date") or new_abnormal_item.get("rem_department"):
    #                     # 更新 rem_reason rem_date rem_department
    #                     item = self.abnormal_dao.get(id=id)
    #                     item.deleted = 1
    #                     item.remove_reason = new_abnormal_item.get("rem_reason")
    #                     item.remove_date = to_datetime(new_abnormal_item.get("rem_date"))
    #                     item.remove_department = new_abnormal_item.get("rem_department")
    #                     self.abnormal_dao.save(item)
    #                     logger.info(f"广东工商-经营异常判定: company_id {company_id} 经营异常信息ID {id}, 移入历史。")
