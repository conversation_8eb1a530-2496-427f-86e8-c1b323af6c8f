import json
import re
from datetime import datetime, date
from typing import Optional
from pydantic import Field
from dateparser import parse
import os

from resx.base_model import BaseModel
from eventlog_spider.common.eventlog_unify import Eventlog
from eventlog_spider.parser.parser import ParserTask, Parser, ParseTools
from resx.config import *
from resx.log import setup_logger
from resx.ext.mysql_rw_splitting import MySQLRWSplittingDao
from biz_utils.msv_write import msv_write_non_ic_company_base_info
from biz_utils.msv_write import MSVSource
from resx.mysql_dao import MySQLDao

logger = setup_logger(name=__name__)


class TwParserTask(ParserTask):
    def __init__(self, eventlog: Eventlog, pages):
        super().__init__(eventlog, pages)


class TwParser(Parser, ParseTools):

    @classmethod
    def get_name(cls):
        return 'tw'

    def __init__(self):
        self.tw_dao = MySQLDao(**CFG_MYSQL_GS_OUTER, db_tb_name='prism.company_tw', primary_index_fields=(['code'], []),
                               ignore_fields=['id', 'company_id', 'create_time'])
        self.supervisor_dao = MySQLDao(**CFG_MYSQL_GS_OUTER, db_tb_name='prism.company_tw_hpd', primary_index_fields=(['company_id'], ['name']),
                                       ignore_fields=['id', 'create_time'])
        self.manager_dao = MySQLDao(**CFG_MYSQL_GS_OUTER, db_tb_name='prism.company_tw_manager', primary_index_fields=(['company_id'], ['name']),
                                    ignore_fields=['id', 'create_time'])
        super().__init__(task_cls=TwParserTask)

    def do_parse(self):
        task: TwParserTask = self.get_parser_task()
        pages = task.pages
        base_info = json.loads(pages['base_info.txt'])
        supervisor = json.loads(pages['supervisor.txt'])
        manager = json.loads(pages['manager.txt'])
        # factory = json.loads(pages['factory.txt'])

        self.tw_dao.save(TW(init=True, **base_info).model_dump())
        pre = self.tw_dao.get(code=base_info['統一編號'])

        temp = []
        for s in supervisor:
            if not s:
                continue
            temp.append({
                'company_id': pre['id'],
                'position': s['職稱'],
                'name': s['姓名'],
                'link_legal_name': s['所代表法人'],
                'capital_num': s.get('出資額(元)',''),
                'equity_num': s.get('持有股份數(股)','')
            })
        self.supervisor_dao.save_group(temp, [pre['id']])

        temp = []
        for m in manager:
            if not m:
                continue
            temp.append({
                'company_id': pre['id'],
                'name': m['姓名'],
                'end_date': parse_minguo_date(m['到職日期']),
            })
        self.manager_dao.save_group(temp, [pre['id']])

        logger.info('台湾解析成功')


class TW(BaseModel):
    code: str = Field(..., description="統一編號")
    name: str = Field(..., description="公司名稱")
    status: str = Field(..., description="登記現況")
    reg_capital: Optional[str] = Field(None, description="資本總額(元)")
    reg_capital_rel: Optional[str] = Field(None, description="实收资本额(元)")
    legal_name: Optional[str] = Field(None, description="代表人姓名")
    address: Optional[str] = Field(None, description="公司所在地")
    reg_institute: Optional[str] = Field(None, description="登記機關")
    approved_time: Optional[date] = Field(None, description="核准設立日期")
    approved_lasttime: Optional[date] = Field(None, description="最後核准變更日期")
    scope: Optional[str] = Field(None, description="所營事業資料")

    def __init__(self, init=False, **data):
        if init:
            field_mapping = {
                '統一編號': 'code',
                "公司名稱": 'name',
                "登記現況": 'status',
                '資本總額(元)': 'reg_capital',
                '实收资本额(元)': 'reg_capital_rel',
                '代表人姓名': 'legal_name',
                '公司所在地': 'address',
                '登記機關': 'reg_institute',
                '核准設立日期': 'approved_time',
                '最後核准變更日期': 'approved_lasttime',
                '所營事業資料': 'scope'
            }
            mapped_data = {}
            for key, value in data.items():
                mapped_key = field_mapping.get(key, key)
                if '日期' in key:
                    mapped_data[mapped_key] = parse_minguo_date(value)
                elif isinstance(value, str):
                    mapped_data[mapped_key] = re.sub(r'\s', '', value.strip())
                else:
                    mapped_data[mapped_key] = value
            super().__init__(**mapped_data)
        else:
            super().__init__(**data)

    class Config:
        populate_by_name = True
        json_encoders = {datetime: lambda dt: dt.strftime('%Y-%m-%d')}


def parse_minguo_date(s: str):
    """
    解析类似 "081年11月28日" 的民国日期，返回对应的公元 datetime。
    """
    m = re.match(r'(\d{1,3})年(\d{1,2})月(\d{1,2})日', s)
    if not m:
        # 非民国格式，fallback 到 dateparser
        return parse(s, languages=['zh'])
    m_y, m_M, m_d = m.groups()
    # 民国年转公元年
    year = int(m_y) + 1911
    # 构造四位年份字符串，再交给 dateparser
    date_str = f"{year:04d}年{int(m_M):02d}月{int(m_d):02d}日"
    return parse(date_str, languages=['zh'])


if __name__ == '__main__':
    os.environ['POD_ENV'] = 'online'
    tw_dao = MySQLRWSplittingDao(
        mysql_ro=CFG_MYSQL_GS_OUTER,
        db_tb_name='prism.company_tw',
        primary_index_fields=(['code'], []),
        # entity_class=TW,
        # dim='',
        ignore_fields=['id', 'company_id', 'create_time'],
    )
    a = {'address': '高雄市鼓山區明誠里明華路349號1樓', 'approved_lasttime': date(2016, 7, 29), 'approved_time': date(2005, 5, 19), 'code': '27227971',
         'legal_name': '麥瓊文', 'name': '木村有限公司', 'reg_capital': '3,000,000', 'reg_capital_rel': None, 'reg_institute': '高雄市政府經濟發展局',
         'scope': 'F501030飲料店業F501060餐館業ZZ99999除許可業務外，得經營法令非禁止或限制之業務', 'status': '核准設立'}
    tw_dao.save(a)
