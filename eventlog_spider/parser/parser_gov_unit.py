import json
from datetime import datetime
from pydantic import Field, conint
from typing import Optional, Union
import re

from resx.base_model import BaseModel
from eventlog_spider.common.eventlog_unify import Eventlog
from eventlog_spider.parser.parser import ParserTask, Parser, ParseTools, check_info
from resx.log import setup_logger
from resx.config import *
from resx.ext.mysql_rw_splitting import MySQLRWSplittingDao
from biz_utils.msv_write import msv_write_non_ic_company_base_info
from biz_utils.msv_write import MSVSource

logger = setup_logger(name=__name__)


class GovUnitParserTask(ParserTask):
    def __init__(self, eventlog: Eventlog, pages):
        super().__init__(eventlog, pages)
        self.log_param_ctx = {'company': check_info(eventlog), 'keyword': check_info(eventlog)}


class GovUnitParser(Parser, ParseTools):

    @classmethod
    def get_name(cls):
        return 'gov_unit'

    def __init__(self):
        self.dao = MySQLRWSplittingDao(
            mysql_ro=CFG_MYSQL_ZX_RDS111,
            db_tb_name='data_experience_situation.gov_unit',
            primary_index_fields=(['us_credit_code'], []),
            entity_class=GovUnit,
            dim='事业单位基本信息',
            ignore_fields=['id', 'company_id', 'create_time']
        )
        self.dao_change = MySQLRWSplittingDao(
            mysql_ro=CFG_MYSQL_ZX_RDS111,
            db_tb_name='data_experience_situation.gov_unit_change',
            primary_index_fields=(['gov_unit_id'], ['change_item', 'change_time']),
            entity_class=GovUnitChange,
            dim='事业单位变更信息',
            ignore_fields=['id']
        )
        self.report_dao = MySQLRWSplittingDao(
            mysql_ro=CFG_MYSQL_ZX_RDS111,
            db_tb_name='data_experience_situation.gov_unit_report',
            primary_index_fields=(['gov_unit_id'], ['year']),
            entity_class=GovUnitReport,
            dim='事业单位年报',
            ignore_fields=['id']
        )
        super().__init__(task_cls=GovUnitParserTask)

    def do_parse(self):
        task: GovUnitParserTask = self.get_parser_task()
        pages = task.pages
        value = {}
        change_fields = {}
        for key, value_ in pages.items():
            value[key] = json.loads(value_)

        try:
            gov_unit: GovUnit = GovUnit(init=True, **value['gov_unit'])
            ret = msv_write_non_ic_company_base_info(credit_no=gov_unit.us_credit_code, source=MSVSource.GOV_UNIT_GJ,
                                                     item=self.remove_no_use(gov_unit.model_dump(mode='json')))
            logger.info(ret)

            name = gov_unit.name
            us_credit_code = gov_unit.us_credit_code
            pre: GovUnit = self.dao.get(us_credit_code=us_credit_code, is_deleted=0)
            if pre:
                change, change_fields = self.compare_print(gov_unit.model_dump(), pre.model_dump(), name, us_credit_code)
                for k, v in change.items():
                    setattr(pre, k, v)
                pre.craw_num += 1
                self.dao.save(pre, task.log_param_ctx)
            else:
                self.dao.save(gov_unit, task.log_param_ctx)
                logger.info(f'insert gov_unit {us_credit_code}')

            gov_unit_id = self.dao.get(us_credit_code=us_credit_code).id
            if value.get('gov_unit_change'):
                list_ = []
                for i in value['gov_unit_change']:
                    gov_unit_change: GovUnitChange = GovUnitChange(**i)
                    gov_unit_change.gov_unit_id = gov_unit_id
                    list_.append(gov_unit_change)
                self.dao_change.save_group(list_, [gov_unit], task.log_param_ctx)

            if value.get('gov_unit_report'):
                gov_unit_report: GovUnitReport = GovUnitReport(init=True, **value['gov_unit_report'])
                gov_unit_report.gov_unit_id = gov_unit_id
                self.report_dao.save(gov_unit_report, task.log_param_ctx)

            task.eventlog.fusion.diff = change_fields
            logger.info(f'解析完成: {gov_unit.name} {gov_unit.us_credit_code} ab_info: {change_fields}')
        except Exception as e:
            logger.error(f"{value['gov_unit']}:")
            self.custom_traceback(e)


class GovUnit(BaseModel, ParseTools):
    id: conint(strict=True, ge=0) = Field(default=0)
    base: str = Field(default='')
    company_id: Optional[conint(strict=True, ge=0)] = Field(default=0)
    name: str = Field(default='')
    old_cert: Optional[str] = Field(default=None)
    us_credit_code: str = Field(default='')
    scope: Optional[str] = Field(default='')
    address: Optional[str] = Field(default='')
    legal_person: Optional[str] = Field(default='')
    expend_source: Optional[str] = Field(default='')
    reg_capital: Optional[str] = Field(default='')
    reg_unit_name: Optional[str] = Field(default='')
    hold_unit: Optional[str] = Field(default='')
    reg_unit: Optional[str] = Field(default='')
    valid_time: Optional[str] = Field(default='')
    reg_time: Optional[str] = Field(default=None)
    reg_status: Optional[str] = Field(default='')
    reg_unit_name_second: Optional[str] = Field(default='')
    reg_unit_name_third: Optional[str] = Field(default='')
    reg_unit_name_other: Optional[str] = Field(default='')
    name_second: Optional[str] = Field(default='')
    name_third: Optional[str] = Field(default='')
    name_other: Optional[str] = Field(default='')
    source: Optional[str] = Field(default='http://search.gjsy.gov.cn/wsss/query')
    create_time: Optional[datetime] = Field(default_factory=lambda: datetime.now().replace(microsecond=0))
    update_time: Optional[datetime] = Field(default_factory=lambda: datetime.now().replace(microsecond=0))
    craw_num: int = Field(default=1)
    is_deleted: int = Field(default=0)

    # model_config = ConfigDict(populate_by_name=True, json_encoders={datetime: lambda dt: dt.strftime('%Y-%m-%d %H:%M:%S')})

    def __init__(self, init=False, **data):
        if init:
            field_mapping = {
                '名称': 'name',
                '统一社会信用代码': 'us_credit_code',
                '宗旨和业务范围': 'scope',
                '住所': 'address',
                '法定代表人': 'legal_person',
                '经费来源': 'expend_source',
                '开办资金': 'reg_capital',
                '举办单位': 'reg_unit_name',
                '登记管理机关': 'hold_unit',
                '有效期': 'valid_time',
                '单位状态': 'reg_status'
            }
            mapped_data = {}
            for key, value in data.items():
                mapped_key = field_mapping.get(key, key)
                mapped_data[mapped_key] = value
            super().__init__(**mapped_data)
        else:
            super().__init__(**data)

        self.base = self.get_base_by_credit_code(self.us_credit_code)
        self.expend_source = self.replace_bracket(self.expend_source)
        self.name = self.replace_bracket(self.name)

    class Config:
        json_encoders = {datetime: lambda dt: dt.strftime('%Y-%m-%d %H:%M:%S')}


class GovUnitChange(BaseModel):
    id: conint(strict=True, ge=0) = Field(default=0)
    gov_unit_id: conint(strict=True, ge=0) = Field(default=0)
    change_item: str = Field(default='')
    change_before: str = Field(default='')
    change_after: str = Field(default='')
    change_time: str = Field(default='')

    def __init__(self, **data):
        super().__init__(**data)
        self.change_time = re.sub(r'[年月]', '-', self.change_time).replace('日', '')


class GovUnitReport(BaseModel, ParseTools):
    id: conint(strict=True, ge=0) = Field(default=0)
    gov_unit_id: conint(strict=True, ge=0) = Field(default=0)
    year: Optional[int] = Field(default=0)
    pub_time: Union[datetime, str] = Field(default='0000-00-00')
    name: Optional[str] = Field(default='')
    legal_cert_no: Optional[str] = Field(default=None)
    us_credit_code: Optional[str] = Field(default='')
    legal_person: Optional[str] = Field(default='')
    expend_source: Optional[str] = Field(default='')
    reg_capital: Optional[str] = Field(default='')
    hold_unit: Optional[str] = Field(default='')
    address: Optional[str] = Field(default='')
    employee: Optional[int] = Field(default=0)
    employee_real: Optional[int] = Field(default=None)
    employee_real_in: Optional[int] = Field(default=None)
    scope: Optional[str] = Field(default='')
    begin_property: Optional[float] = Field(default=0)
    end_property: Optional[float] = Field(default=0)
    contact_name: Optional[str] = Field(default=None)
    contact_phone: Optional[str] = Field(default=None)
    contact_email: Optional[str] = Field(default=None)
    situation1: Optional[str] = Field(default='')
    situation2: Optional[str] = Field(default='')
    situation3: Optional[str] = Field(default='')
    situation4: Optional[str] = Field(default='')
    situation5: Optional[str] = Field(default='')
    remark: Optional[str] = Field(default='')
    source: Optional[str] = Field(default='')

    def __init__(self, init=False, **data):
        if init:
            if not isinstance(data['pub_time'], datetime):
                data['pub_time'] = re.sub(r'[年月]', '-', data['pub_time']).replace('日', '') if data.get('pub_time') else '0000-00-00'
            field_mapping = {
                '法定代表人': 'legal_person',
                '经费来源': 'expend_source',
                '开办资金': 'reg_capital',
                '举办单位': 'hold_unit',
                '住所': 'address',
                '从业人数': 'employee',
                '宗旨和业务范围': 'scope',
                '资产损益情况 净资产合计（所有者权益合计）年初数（万元）': 'begin_property',
                '资产损益情况 净资产合计（所有者权益合计）年末数（万元）': 'end_property',
                '对《条例》和实施细则有关变更登记规定的执行情况': 'situation1',
                '开展业务活动情况': 'situation2',
                '相关资质认可或执业许可证明文件及有效期': 'situation3',
                '绩效和受奖惩及诉讼投诉情况': 'situation4',
                '接受捐赠资助及使用情况': 'situation5'
            }
            mapped_data = {}
            for key, value in data.items():
                mapped_key = field_mapping.get(key, key)
                mapped_data[mapped_key] = value
            super().__init__(**mapped_data)
        else:
            super().__init__(**data)

        self.reg_capital = self.remove_bracket(self.reg_capital)
        self.expend_source = self.add_brackets(self.expend_source)
        self.situation2 = re.sub(r'[\r\n\t]', '', self.situation2)

    class Config:
        populate_by_name = True
        json_encoders = {datetime: lambda dt: dt.strftime('%Y-%m-%d')}


if __name__ == '__main__':
    pass
