# encoding=utf8
from abc import abstractmethod
from threading import Lock, current_thread
from typing import Dict
from prettytable import PrettyTable
from datetime import datetime, date
import traceback
import re
import resx.func
from eventlog_spider.common.eventlog import Eventlog, SpiderCode, EventlogOld
from eventlog_spider.common.eventlog_unify import StatusCode
from eventlog_spider.common.obs_manager import OBSManager
from resx.log import setup_logger
from crawler_log.log_init_v2 import Log
from collections import OrderedDict
from eventlog_spider.common.eventlog_unify import FusionDiffDetail

logger = setup_logger(name=__name__)


def check_info(eventlog: Eventlog):
    info = eventlog.selector.info
    if hasattr(eventlog.selector, 'word') or 'keyword' in info:
        return eventlog.selector.get_info('keyword') or eventlog.selector.word
    else:
        for k, v in info.items():
            if re.search('[A-Z0-9]{18}', v):
                return v
        else:
            k, v = next(iter(info.items()))
            return v


class ParserTask(object):
    def __init__(self, eventlog: Eventlog, pages):
        self.eventlog: Eventlog = eventlog
        self.event_id = self.eventlog.event_id
        self.pages: Dict[str, str] = pages
        self.log_param_ctx: Dict[str, str] = {}

        info = eventlog.selector.info
        if hasattr(eventlog.selector, 'word') or 'keyword' in info:
            self.keyword = eventlog.selector.get_info('keyword') or eventlog.selector.word
        else:
            for k, v in info.items():
                if re.search('[A-Z0-9]{18}', v):
                    self.keyword = v
                    break
            else:
                k, v = next(iter(info.items()))
                self.keyword = v

        self.log_param_ctx['keyword'] = self.keyword
        self.log_param_ctx['company'] = self.keyword
        if isinstance(logger, Log):
            logger.ParamManger().add_params(**self.log_param_ctx)
        if hasattr(eventlog, 'parser') and not hasattr(eventlog, 'parser'):
            eventlog.parser["processId"] = f"{resx.func.get_my_ip()}-{current_thread().ident}"


class Parser(object):
    def __init__(self, task_cls):
        self.task_cls = task_cls
        self.obs_manager = OBSManager()
        self.tasks: Dict[int, task_cls] = dict()
        self.tasks_lock = Lock()
        # self.eventlog_store = EventlogStore(max_workers=2)

    @classmethod
    @abstractmethod
    def get_name(cls):
        pass

    def parse(self, eventlog: Eventlog) -> Eventlog:
        logger.info(f'==== BEGIN {eventlog}')

        # filter bad eventlog
        if hasattr(eventlog, 'spider_code') and eventlog.spider_code != SpiderCode.UNFILLED:
            return eventlog
        if hasattr(eventlog, 'code') and eventlog.code != StatusCode.UNPROCESSED:
            return eventlog

        if hasattr(eventlog, 'spider'):
            page_ts = eventlog.spider.spider_data.get('page_ts', 0)
            if page_ts == 0:
                eventlog.spider_code = SpiderCode.FAIL
                logger.warning(f'page_ts bad {eventlog.event_id}')
                return eventlog
        else:
            page_ts = eventlog.parser.data.get('page_ts', 0)
            if page_ts == 0:
                eventlog.code = StatusCode.GENERAL_ERROR_RETRY
                logger.warning(f'page_ts bad {eventlog.event_id}')
                return eventlog

        pages_obs_path = f'page/{self.get_name()}/{check_info(eventlog)}/{page_ts}'
        pages = self.obs_manager.download_pages(pages_obs_path)

        with self.tasks_lock:
            tid = current_thread().ident
            self.tasks[tid] = self.task_cls(eventlog, pages)
        self.do_parse()

        if hasattr(eventlog, 'spider_code') and eventlog.spider_code == SpiderCode.UNFILLED:
            self.post_parse()
        if hasattr(eventlog, 'code') and eventlog.code == StatusCode.UNPROCESSED:
            self.post_parse()

        # 最后设置成功
        if hasattr(eventlog, 'spider_code') and eventlog.spider_code == SpiderCode.UNFILLED:
            eventlog.spider_code = SpiderCode.SUCCESS
            if isinstance(eventlog, EventlogOld):
                eventlog.crawler_code = 0
                eventlog.parser_code = 0
        if hasattr(eventlog, 'code') and eventlog.code == StatusCode.UNPROCESSED:
            eventlog.code = StatusCode.SUCCESS

        return eventlog

    @abstractmethod
    def do_parse(self):
        pass

    def post_parse(self):
        # task: ParserTask = self.get_parser_task()
        # if eventlog.is_clue:
        # eventlog.spider.item_insert 这个需要 业务自己处理 parser_gsxt已处理
        # 设置 eventlog.selector.info['entry_word'] 以及 eventlog.selector.info['entry_*']
        pass

    def get_parser_task(self):
        with self.tasks_lock:
            tid = current_thread().ident
            if tid not in self.tasks:
                raise RuntimeError(f'not task tid={tid}')
            return self.tasks[tid]


class ParseTools:
    @staticmethod
    def replace_bracket(name, cn_to_en=False):
        if cn_to_en:
            return name.replace('（', '(').replace('）', ')')
        return name.replace('(', '（').replace(')', '）')

    @staticmethod
    def get_base_by_credit_code(credit_code) -> str:
        if credit_code:
            code = credit_code[2:4]
            if code.startswith("10"):
                return "gj"
            elif code.startswith("11"):
                return "bj"
            elif code.startswith("12"):
                return "tj"
            elif code.startswith("13"):
                return "heb"
            elif code.startswith("14"):
                return "sx"
            elif code.startswith("15"):
                return "nmg"
            elif code.startswith("21"):
                return "ln"
            elif code.startswith("22"):
                return "jl"
            elif code.startswith("23"):
                return "hlj"
            elif code.startswith("31"):
                return "sh"
            elif code.startswith("32"):
                return "js"
            elif code.startswith("33"):
                return "zj"
            elif code.startswith("34"):
                return "ah"
            elif code.startswith("35"):
                return "fj"
            elif code.startswith("36"):
                return "jx"
            elif code.startswith("37"):
                return "sd"
            elif code.startswith("41"):
                return "hen"
            elif code.startswith("42"):
                return "hub"
            elif code.startswith("43"):
                return "hun"
            elif code.startswith("44"):
                return "gd"
            elif code.startswith("45"):
                return "gx"
            elif code.startswith("46"):
                return "han"
            elif code.startswith("50"):
                return "cq"
            elif code.startswith("51"):
                return "sc"
            elif code.startswith("52"):
                return "gz"
            elif code.startswith("53"):
                return "yn"
            elif code.startswith("54"):
                return "xz"
            elif code.startswith("61"):
                return "snx"
            elif code.startswith("62"):
                return "gs"
            elif code.startswith("63"):
                return "qh"
            elif code.startswith("64"):
                return "nx"
            elif code.startswith("65"):
                return "xj"
            elif code.startswith("71"):
                return "tw"
            elif code.startswith("81"):
                return "hk"
            elif code.startswith("82"):
                return "mo"
            else:
                return 'gj'
        else:
            return ''

    @staticmethod
    def truncate_text(text, max_length=70):
        text = str(text)
        if len(text) > max_length:
            return text[:max_length] + "..."
        return text

    @staticmethod
    def remove_bracket(name):
        return re.sub(r'[()（）]', '', name)

    @staticmethod
    def add_brackets(text):
        patterns = ['经费自理', '全额拨款', '自收自支', '差额拨款', '财政拨款']
        for pattern in patterns:
            text = re.sub(pattern, f'（{pattern}）', text)
        return text

    def compare_print(self, change_data: dict, old_data: dict, name: str = '', us_credit_code: str = '', lawyer_name='', License='', none_cover=True,
                      ignore_fields: tuple = (), is_print=True):
        if not change_data:
            return {}

        update_fields = {}
        for key, value in change_data.items():
            if not none_cover and (value == '' or value is None):
                continue
            if key in old_data and value != old_data[key]:
                update_fields[key] = [old_data[key], value]

        ignore_fields2 = ('id', 'deleted', 'create_time', 'update_time', 'source', 'company_id', 'createTime',
                          'is_lawfirm', 'craw_num', 'company_gid', 'crawledtime', 'updatetime', 'crawl_time')
        table = PrettyTable()
        table.field_names = ['key', "old", "new"]
        for key, (old, new) in update_fields.items():
            if key in ignore_fields + ignore_fields2:
                continue
            table.add_row([key, self.truncate_text(old), self.truncate_text(new)])
        table.max_width['old'] = 70
        table.max_width['new'] = 70
        table.max_width['key'] = 24
        if is_print:
            logger.info(f'{name}-{us_credit_code}-{lawyer_name}-{License}\n' + str(table))

        update_fields2 = {}
        for k, v in update_fields.items():
            update_fields2[k] = v[1]
        for k in ignore_fields2 + ignore_fields:
            try:
                del update_fields[k]
            except:
                continue
        diff = {}
        for k, v in update_fields.items():
            old, new = v
            diff[k] = FusionDiffDetail(before=old, after=new)
        return update_fields2, diff

    @staticmethod
    def custom_traceback(e):
        exc_type = type(e)
        exc_value = e
        tb = e.__traceback__
        """
        自定义打印 traceback，仅显示用户代码的报错信息
        """
        # 提取 traceback 信息
        tb_list = traceback.extract_tb(tb)
        user_tb = [frame for frame in tb_list if 'eventlog-spider' in frame.filename]
        error = ''
        if user_tb:
            print("\033[91mTraceback (most recent call last):\033[0m")
            # 格式化并打印用户代码的 traceback 信息
            print("\033[91m" + "".join(traceback.format_list(user_tb)) + "\033[0m")
            print(f"\033[91m{exc_type.__name__}: {exc_value}\033[0m")
            error += "".join(traceback.format_list(user_tb)) + f"{exc_type.__name__}: {exc_value}"
        else:
            print(f"\033[91m{exc_type.__name__}: {exc_value}\033[0m")
            error += f"{exc_type.__name__}: {exc_value}"
        return error

    @staticmethod
    def unique(my_list, exclude_field: str = ''):
        unique_list = []
        # history = {}
        for d in my_list:
            filtered_items = tuple((k, v) for k, v in sorted(d.items()) if k != exclude_field)
            unique_list.append(filtered_items)
            # history.update({d["uniscid"]: d[exclude_field]})
        unique_list = list(OrderedDict.fromkeys(unique_list))
        data = []
        for t in unique_list:
            d = dict(t)
            # d.update({exclude_field: history[d['uniscid']]})
            data.append(d)
        return data

    @staticmethod
    def remove_no_use(item: dict[str, str]):
        for i in ['id', 'deleted', 'create_time', 'update_time', 'source', 'company_id', 'createTime',
                  'is_lawfirm', 'craw_num', 'company_gid', 'crawledtime', 'updatetime', 'crawl_time',
                  'base', 'is_deleted', 'legal_person_id', 'org_source', 'org_province']:
            try:
                del item[i]
            except:
                continue
        return item

    @staticmethod
    def datatime_to_str(item: dict):
        for k, v in item.items():
            if isinstance(v, datetime) or isinstance(v, date):
                item[k] = v.strftime('%Y-%m-%d')
