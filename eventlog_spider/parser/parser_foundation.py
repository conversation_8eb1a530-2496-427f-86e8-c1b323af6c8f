import json
import re
from datetime import datetime, date
from pydantic import Field, conint
from typing import Optional

from resx.func import to_date
from resx.base_model import BaseModel
from eventlog_spider.common.eventlog_unify import Eventlog
from eventlog_spider.parser.parser import ParserTask, Parser, ParseTools
from resx.config import *
from resx.log import setup_logger
from resx.ext.mysql_rw_splitting import MySQLRWSplittingDao
from biz_utils.msv_write import msv_write_non_ic_company_base_info
from biz_utils.msv_write import MSVSource
from eventlog_spider.common.eventlog_unify import FusionDiffDetail

logger = setup_logger(name=__name__)


class FoundationParserTask(ParserTask):
    pass


class FoundationParser(Parser, ParseTools):

    @classmethod
    def get_name(cls):
        return 'foundation'

    def __init__(self):
        self.Foundation_dao = MySQLRWSplittingDao(
            mysql_ro=CFG_MYSQL_GS_OUTER,
            db_tb_name='prism.foundation',
            primary_index_fields=(['creditCode'], []),
            entity_class=FoundationEntity,
            ignore_fields=['id', 'createTime', 'company_id']
        )
        self.baseinfo_transparency_dao = MySQLRWSplittingDao(
            mysql_ro=CFG_MYSQL_GS_OUTER,
            db_tb_name='prism.foundation_baseinfo_transparency',
            primary_index_fields=(['foundationId'], []),
            entity_class=FoundationBaseInfoTransparencyEntity,
            ignore_fields=['id', 'createTime']
        )
        self.gov_staff_dao = MySQLRWSplittingDao(
            mysql_ro=CFG_MYSQL_GS_OUTER,
            db_tb_name='prism.foundation_staff',
            primary_index_fields=(['foundationId'], ['name']),
            entity_class=FoundationGovStaffEntity,
            ignore_fields=['id']
        )
        self.finance_dao = MySQLRWSplittingDao(
            mysql_ro=CFG_MYSQL_GS_OUTER,
            db_tb_name='prism.foundation_finance',
            primary_index_fields=(['foundationId'], ['year']),
            entity_class=FoundationFinanceEntity,
            ignore_fields=['id']
        )
        self.project_dao = MySQLRWSplittingDao(
            mysql_ro=CFG_MYSQL_GS_OUTER,
            db_tb_name='prism.foundation_project',
            primary_index_fields=(['foundationId'], ['projectName']),
            entity_class=FoundationProjectEntity,
            ignore_fields=['id']
        )
        super().__init__(task_cls=FoundationParserTask)

    def do_parse(self):
        task: FoundationParserTask = self.get_parser_task()
        eventlog: Eventlog = task.eventlog
        pages = task.pages

        base_info = json.loads(pages['base_info.json'])
        capital = re.match(r'(\d*)', base_info.get('原始基金', ''))
        capital = capital.group(1) + '万元人民币' if capital else ''
        foundation: FoundationEntity = FoundationEntity.from_dict({
            'name': base_info.get('name', ''),
            'base': self.get_base_by_credit_code(base_info.get('信用代码', '')),
            'creditCode': base_info.get('信用代码', ''),
            'purpose': base_info.get('宗旨', '').replace('\n', ''),
            'businessScope': base_info.get('业务范围', ''),
            'department': base_info.get('登记部门', ''),
            'establishTime': to_date(base_info.get('成立时间', '')),
            'originalAmount': capital,
            'address': base_info.get('地址', ''),
            'website': base_info.get('网址', ''),
            'chairman': base_info.get('理事长', ''),
            'secretary': base_info.get('秘书长', ''),
            "telephone": base_info.get('电话', ''),
            "fax": base_info.get('传真', ''),
            "email": base_info.get('邮箱', ''),
        })
        ret = msv_write_non_ic_company_base_info(credit_no=foundation.creditCode, source=MSVSource.FOUNDATION,
                                                 item=self.remove_no_use(foundation.model_dump(mode='json')))
        logger.info(ret)

        pre = self.Foundation_dao.get(creditCode=base_info['信用代码'])
        change_fields = {}
        if pre:
            _, change_fields = self.compare_print(foundation.model_dump(), pre.model_dump(), foundation.name, foundation.creditCode)
        else:
            logger.log(f'insert foundation {foundation.creditCode}')
        self.Foundation_dao.save(foundation, task.log_param_ctx)
        foundation = self.Foundation_dao.get(creditCode=base_info['信用代码'])

        transparency_entity: FoundationBaseInfoTransparencyEntity = FoundationBaseInfoTransparencyEntity.from_dict({
            "foundationId": foundation.id,
            "constitution": 1 if base_info.get('章程') else 0,
            "originalAmount": 1 if base_info.get('原始基金') else 0,
            "purpose": 1 if base_info.get('宗旨') else 0,
            "telephone": 1 if base_info.get('电话') else 0,
            "address": 1 if base_info.get('地址') else 0,
            "directorName": 1 if base_info.get('理事长') else 0,
            # "score": base_info.get('score', '')[1] if base_info.get('score', '') else '',
        })
        self.baseinfo_transparency_dao.save(transparency_entity)

        id_ = foundation.id
        staff_list = []
        for council in json.loads(pages['councils.json']):
            entity: FoundationGovStaffEntity = FoundationGovStaffEntity.from_dict({"foundationId": id_, "name": council})
            staff_list.append(entity)
        for supervisor in json.loads(pages['supervisor.json']):
            entity: FoundationGovStaffEntity = FoundationGovStaffEntity.from_dict({"foundationId": id_, "name": supervisor, 'type': 1})
            staff_list.append(entity)
        delete_count, insert_count, update_count = self.gov_staff_dao.save_group(staff_list, [id_])
        change_count = delete_count + insert_count + update_count

        finances = json.loads(pages['finance.json'])
        for year, finance in finances.items():
            finance_entity: FoundationFinanceEntity = FoundationFinanceEntity.from_dict({
                "foundationId": foundation.id,
                "netAssets": finance.get('净资产', ''),
                "donationIncome": finance.get('捐赠收入', ''),
                "publicWelfareExpenditure": finance.get('公益支出', ''),
                "annualIncome": finance.get('年度总收入', ''),
                "year": year,
            })
            self.finance_dao.save(finance_entity)

        projects = json.loads(pages['project.json'])
        for year, project_ in projects.items():
            for project in project_:
                project_entity: FoundationProjectEntity = FoundationProjectEntity.from_dict({
                    "foundationId": foundation.id,
                    "projectName": project[0],
                    "income": project[1],
                    "expenditure": project[2],
                    "foundationName": base_info.get('name', ''),
                    "year": year,
                    "projectBrief": project[3],
                })
                self.project_dao.save(project_entity)

        change_fields['staff_change_count'] = FusionDiffDetail(before=0, after=change_count)
        eventlog.fusion.diff = change_fields
        logger.info(f'解析完成: {foundation.name} {foundation.creditCode} ab_info: {eventlog.fusion.diff}')


class FoundationEntity(BaseModel):
    id: conint(strict=True, ge=0) = Field(default=0)
    company_id: Optional[conint(strict=True, ge=0)] = Field(default=0)
    name: Optional[str] = Field(default='')
    base: Optional[str] = Field(default='')
    creditCode: Optional[str] = Field(default='')
    purpose: Optional[str] = Field(default='')
    businessScope: Optional[str] = Field(default='')
    department: Optional[str] = Field(default='')
    establishTime: Optional[date] = Field(default=datetime.strptime('0001-01-01', '%Y-%m-%d').date())
    originalAmount: Optional[str] = Field(default='')
    address: Optional[str] = Field(default='')
    website: Optional[str] = Field(default='')
    chairman: Optional[str] = Field(default='')
    secretary: Optional[str] = Field(default='')
    telephone: Optional[str] = Field(default='')
    fax: Optional[str] = Field(default='')
    email: Optional[str] = Field(default='')
    createTime: Optional[datetime] = Field(default_factory=datetime.now)
    deleted: int = Field(default=0)

    class Config:
        populate_by_name = True
        json_encoders = {datetime: lambda dt: dt.strftime('%Y-%m-%d %H:%M:%S')}


class FoundationBaseInfoTransparencyEntity(BaseModel):
    id: conint(strict=True, ge=0) = Field(default=0)
    foundationId: conint(strict=True, ge=0) = Field(default=0)
    constitution: Optional[int] = Field(default=0)
    originalAmount: Optional[int] = Field(default=0)
    purpose: Optional[int] = Field(default=0)
    telephone: Optional[int] = Field(default=0)
    address: Optional[int] = Field(default=0)
    directorName: Optional[int] = Field(default=0)
    score: Optional[str] = Field(default=0)
    createTime: datetime = Field(default_factory=datetime.now)
    deleted: int = Field(default=0)


class FoundationGovStaffEntity(BaseModel):
    id: conint(strict=True, ge=0) = Field(default=0)
    foundationId: conint(strict=True, ge=0) = Field(default=0)
    name: Optional[str] = Field(default='')
    position: Optional[str] = Field(default='')
    gender: Optional[str] = Field(default='')
    meetingCount: Optional[str] = Field(default='')
    companyAndPostion: Optional[str] = Field(default='')
    type: Optional[int] = Field(default=0)
    deleted: int = Field(default=0)


class FoundationFinanceEntity(BaseModel):
    id: conint(strict=True, ge=0) = Field(default=0)
    foundationId: conint(strict=True, ge=0) = Field(default=0)
    netAssets: Optional[str] = Field(default='')
    donationIncome: Optional[str] = Field(default='')
    publicWelfareExpenditure: Optional[str] = Field(default='')
    annualIncome: Optional[str] = Field(default='')
    year: Optional[str] = Field(default='')
    deleted: int = Field(default=0)


class FoundationProjectEntity(BaseModel):
    id: conint(strict=True, ge=0) = Field(default=0)
    foundationId: conint(strict=True, ge=0) = Field(default=0)
    projectName: Optional[str] = Field(default='')
    income: Optional[str] = Field(default='')
    expenditure: Optional[str] = Field(default='')
    foundationName: Optional[str] = Field(default='')
    year: Optional[str] = Field(default='')
    projectBrief: Optional[str] = Field(default='')
    deleted: int = Field(default=0)
