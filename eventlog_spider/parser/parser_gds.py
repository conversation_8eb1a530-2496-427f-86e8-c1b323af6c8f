import json
from datetime import datetime
from pydantic import Field, conint
from typing import Optional

from resx.base_model import BaseModel
from eventlog_spider.common.eventlog_unify import Eventlog
from eventlog_spider.parser.parser import ParserTask, Parser, ParseTools
from resx.log import setup_logger
from resx.config import *
from resx.ext.mysql_rw_splitting import MySQLRWSplittingDao

logger = setup_logger(name=__name__)


class GdsParserTask(ParserTask):
    pass


class GdsParser(Parser, ParseTools):
    @classmethod
    def get_name(cls):
        return 'gds_company'

    def __init__(self):
        self.firm_dao = MySQLRWSplittingDao(
            mysql_ro=CFG_MYSQL_GS_INNER,
            db_tb_name='prism.gds_firm',
            primary_index_fields=(['firm_id'], []),
            entity_class=FirmEntity,
            dim='',
            ignore_fields=['id', 'create_time']
        )
        self.brand_dao = MySQLRWSplittingDao(
            mysql_ro=CFG_MYSQL_GS_INNER,
            db_tb_name='prism.gds_brand',
            primary_index_fields=(['brand_id', 'cn_name'], []),
            entity_class=BrandEntity,
            dim='',
            ignore_fields=['id', 'create_time']
        )
        self.product_dao = MySQLRWSplittingDao(
            mysql_ro=CFG_MYSQL_GS_INNER,
            db_tb_name='prism.gds_product',
            primary_index_fields=(['barcode'], []),
            entity_class=ProductEntity,
            dim='',
            ignore_fields=['id', 'create_time']
        )
        super().__init__(task_cls=GdsParserTask)

    def do_parse(self):
        task: GdsParserTask = self.get_parser_task()
        eventlog: Eventlog = task.eventlog
        pages = task.pages

        if pages.get('无新条形码'):
            logger.info('无新条形码')
            return

        company_info: dict = json.loads(pages.get('company_info.txt'))
        brand_infos: list = json.loads(pages['brand_info.txt'])
        products_infos: list = json.loads(pages['products_info.txt'])

        if not products_infos:
            return

        # for i in company_info['fid_old']:
        #     self.product_dao.execute(f"update prism.gds_product set deleted = 1 where firm_id = {i}")

        productcount = company_info['productcount']
        if productcount < 10000:
            barcode_pre = [product.barcode for product in self.product_dao.get_many(firm_id=company_info['fid'])]
            barcode = company_info['barcodes']
            barcode_diff = list(set(barcode_pre) - set(barcode))
            for b in barcode_diff:
                self.product_dao.execute(f"update prism.gds_product set deleted = 1 where barcode = '{b}'")

        firm_ent = FirmEntity.from_dict(
            {
                'firm_id': int(company_info.get('fid', 0)),
                # 'cn_name': company_info.get('firmname', ''),
                'cn_name': eventlog.selector.info['company_name'],
                'en_name': company_info.get('englishname', ''),
                'company_id': int(eventlog.selector.info['company_id']),
                'address': company_info.get('registeraddress', ''),
                'register_date': datetime.strptime(company_info['registertime'][:10], '%Y-%m-%d') if company_info.get(
                    'registertime', '') != '' else datetime.strptime('0001-01-01', '%Y-%m-%d'),
                'website': company_info.get('website', '').strip(),
                'product_count': company_info.get('productcount', 0),
                'brand_count': company_info.get('brandcount', 0),
                'micro_site': company_info.get('ismicrosite', 0),
                # 'certificate_code': company_info.get('certifycatecode', '').strip(),
                'certificate_code': eventlog.selector.info['credit_no'],
                # 'json_data': company_info
            }
        )
        self.firm_dao.save(firm_ent)

        for brand_info in brand_infos:
            if brand_info.get('brand_id', 0):
                brand_ent = BrandEntity.from_dict({
                    'firm_id': int(company_info.get('fid', 0)),
                    'brand_id': int(brand_info.get('brand_id', 0)),
                    'cn_name': brand_info.get('brandcn', ''),
                    'en_name': brand_info.get('branden', ''),
                    'intro': brand_info.get('gpcnamecn', ''),
                    'certified': brand_info.get('brandcertified', 0),
                    # 'brand_create_time': datetime.strptime(brand_info.get('UpdateTime', '0001-01-01')[:10], '%Y-%m-%d'),
                    'brand_create_time': datetime.strptime('1970-01-01', '%Y-%m-%d'),
                    # 'json_data': brand_info
                })
                self.brand_dao.save(brand_ent)

        for products_info in products_infos:
            barcode = products_info['GTIN']
            NetContent = products_info['NetContent'] if products_info['NetContent'] else ''
            NetContentUnitofMeasureDescription = products_info['NetContentUnitofMeasureDescription'] if products_info[
                'NetContentUnitofMeasureDescription'] else ""
            GcpCodeStatusCode = products_info['GcpCodeStatusCode']

            product_ent = ProductEntity.from_dict({
                'firm_id': int(products_info.get('FirmID', 0)),
                'brand_id': int(products_info.get('BrandID', 0)),
                'product_id': int(products_info.get('ID', 0)),
                'product_desc': products_info.get('ProductDescription', '').replace(' ', ''),
                'product_img_paths': '',
                'barcode': barcode,
                # 'barcode_img_path': products_info.get('barcode_pic', ''),
                # 'barcode_status': products_info.get('gtinstatus', 0),
                'barcode_status': 1 if GcpCodeStatusCode == 0 else 2,
                'gpc_code': products_info.get('GlobalProductCategoryCode', '') or '',
                'gpc_name': products_info.get('GlobalProductCategoryName', '') or '',
                'first_ship_date': datetime.strptime(products_info['FirstShipDateTime'][:10], '%Y-%m-%d') if
                products_info['FirstShipDateTime'] else datetime.strptime('0001-01-01', '%Y-%m-%d'),
                'product_spec': NetContent + NetContentUnitofMeasureDescription,
                'product_net_content': products_info.get('NetContentStatement', '') or '',
                'crawl_status': 0,
                # 'json_data': products_info
            })
            self.product_dao.save(product_ent)

        logger.info(f'task: {eventlog}')


class FirmEntity(BaseModel):
    id: conint(strict=True, ge=0) = Field(default=0)
    firm_id: int = Field(default=0)
    cn_name: str = Field(default='')
    en_name: str = Field(default='')
    company_id: conint(strict=True, ge=0) = Field(default=0)
    address: str = Field(default='')
    register_date: Optional[datetime] = Field(default=datetime.strptime('0001-01-01', '%Y-%m-%d'))
    website: str = Field(default='')
    product_count: conint(strict=True, ge=0) = Field(default=0)
    brand_count: conint(strict=True, ge=0) = Field(default=0)
    micro_site: int = Field(default=0)
    certificate_code: str = Field(default='')
    # json_data: dict
    create_time: Optional[datetime] = Field(default_factory=datetime.now)
    update_time: Optional[datetime] = Field(default_factory=datetime.now)
    deleted: int = Field(default=0)


class BrandEntity(BaseModel):
    id: conint(strict=True, ge=0) = Field(default=0)
    firm_id: conint(strict=True, ge=0) = Field(default=0)
    brand_id: conint(strict=True, ge=0) = Field(default=0)
    cn_name: str = Field(default='')
    en_name: str = Field(default='')
    intro: str = Field(default='')
    certified: int = Field(default=0)
    brand_create_time: Optional[datetime] = Field(default=datetime.strptime('0001-01-01', '%Y-%m-%d'))
    # json_data: dict
    create_time: Optional[datetime] = Field(default_factory=datetime.now)
    update_time: Optional[datetime] = Field(default_factory=datetime.now)
    deleted: int = Field(default=0)


class ProductEntity(BaseModel):
    id: conint(strict=True, ge=0) = Field(default=0)
    firm_id: conint(strict=True, ge=0) = Field(default=0)
    brand_id: conint(strict=True, ge=0) = Field(default=0)
    product_id: conint(strict=True, ge=0) = Field(default=0)
    product_desc: str = Field(default='')
    product_img_paths: str = Field(default='')
    barcode: str = Field(default='')
    barcode_img_path: str = Field(default='')
    barcode_status: int = Field(default=0)
    gpc_code: str = Field(default='')
    gpc_name: str = Field(default='')
    first_ship_date: Optional[datetime] = Field(default=datetime.strptime('0001-01-01', '%Y-%m-%d'))
    product_spec: str = Field(default='')
    product_net_content: str = Field(default='')
    crawl_status: int = Field(default=0)
    # json_data: dict
    create_time: Optional[datetime] = Field(default_factory=datetime.now)
    update_time: Optional[datetime] = Field(default_factory=datetime.now)
    deleted: int = Field(default=0)
