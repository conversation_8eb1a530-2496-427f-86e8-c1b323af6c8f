from urllib3 import disable_warnings
from resx.log import setup_logger
from resx.mysql_client import MySQLClient
from resx.config import *
import os

logger = setup_logger(use_crawler_log=True, crawler_log_enable=False, name=__name__, debug=False)
from eventlog_spider.crawler.crawler_enterprise_standard import EnterpriseStandardCrawler as Crawler
from eventlog_spider.parser.parser_enterprise_standard import EnterpriseStandardParser as Parser
from eventlog_spider.common.eventlog_unify import Eventlog

disable_warnings()
mysql = MySQLClient(**CFG_MYSQL_GS_OUTER)
history_name_dao = MySQLClient(**CFG_MYSQL_GS_OUTER)

os.environ['POD_ENV'] = 'online'


def get_eventlog():
    return Eventlog.from_dict(
        {
            "event_id": "octopus_entry-JkcEhuuh-fdc6185001caf16d7cf42044b5bfac8c-enterprise_standard-1755509477611",
            "code": -1,
            "selector": {
                "reason": "normal_schedule",
                "crawler_id": "Jkc<PERSON>huuh",
                "crawler_name": "enterprise_standard",
                "dimension_name": "企业标准信息",
                "score": 1.1755509478,
                "from_cache": False,
                "receive_time": "2025-08-18 17:31:17",
                "send_time": "2025-08-18 17:31:17",
                "try_id": 0,
                "info": {
                    "orgName": "安徽华上电缆科技有限公司"
                }
            },
            "crawler": {
                "code": -1,
                "receive_time": "",
                "send_time": "",
                "model": False
            },
            "parser": {
                "code": -1,
                "receive_time": "",
                "send_time": "",
                "model": False
            },
            "fusion": {
                "code": -1,
                "receive_time": "",
                "send_time": "",
                "model": False
            },
            "channel": {},
            "dimensions": {}
        }
    )


crawler = Crawler()
parser = Parser()

a = get_eventlog()
crawler.crawl(a)
parser.parse(a)
logger.info(f'after parser {a.model_dump_json()}')
