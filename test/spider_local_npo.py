import os
from urllib3 import disable_warnings

# os.environ['POD_ENV'] = 'online'

from resx.log import setup_logger

logger = setup_logger(use_crawler_log=True, crawler_log_enable=True, name=__name__, debug=False)

from eventlog_spider.crawler.crawler_npo import <PERSON><PERSON>ler<PERSON><PERSON> as Crawler
from eventlog_spider.parser.parser_npo import Parser<PERSON><PERSON> as Parser
from eventlog_spider.common.eventlog_unify import Eventlog

disable_warnings()

eventlog = Eventlog.from_dict(
    {
        "event_id": "octopus_entry-EztiEUJq-ada739cf85b5be52dd3c1a1463679ee2-npo-1754979920978",
        "code": -1,
        "selector": {
            "reason": "normal_schedule",
            "crawler_id": "EztiEUJq",
            "crawler_name": "npo",
            "dimension_name": "社会组织",
            "score": 1.1754979921,
            "from_cache": False,
            "receive_time": "2025-08-12 14:25:20",
            "send_time": "2025-08-12 14:25:20",
            "try_id": 0,
            "info": {
                "name": "锡林郭勒盟金融会计学会",
                "unified_social_credit_code": "52320923MJ751963XD"
            }
        },
        "crawler": {
            "code": -1,
            "receive_time": "",
            "send_time": "",
            "model": False
        },
        "parser": {
            "code": -1,
            "receive_time": "",
            "send_time": "",
            "model": False
        },
        "fusion": {
            "code": -1,
            "receive_time": "",
            "send_time": "",
            "model": False
        },
        "channel": {},
        "dimensions": {}
    }
)

crawler = Crawler()
parser = Parser()

crawler.crawl(eventlog)
parser.parse(eventlog)
logger.info(f'after parser {eventlog.model_dump_json()}')
