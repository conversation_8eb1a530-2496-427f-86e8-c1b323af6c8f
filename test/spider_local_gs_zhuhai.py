# encoding=utf8
from urllib3 import disable_warnings
from resx.log import setup_logger

logger = setup_logger(use_crawler_log=True, crawler_log_enable=False, name=__name__, debug=False)

from eventlog_spider.common.eventlog import EventlogOld, SpiderCode
from eventlog_spider.crawler.crawler_gs_zhuhai import <PERSON>hai<PERSON><PERSON><PERSON> as Crawler
from eventlog_spider.parser.parser_gs_zhuhai import ZhuhaiParser as Parser

disable_warnings()

eventlog = EventlogOld.from_dict(
    {
        "event_id": "octopus_entry-company-*********-gdzh-1755051296",
        "is_clue": False,
        "spider_code": -1,
        "crawlerType": 1,
        "crawlerCode": -1,
        "parserCode": -1,
        "fusionCode": -1,
        "selector": {
            "send_ts": 1755051296,
            "receive_ts": -1,
            "reason": "schedule",
            "item_name": "company",
            "inst_name": "gdzh",
            "word": "2456553153",
            "info": {
                "name": "珠海市艺滑星体育文化有限公司",
                "credit_code": "91440404MACG01YR3Q",
                "reg_number": "***************",
                "reg_status": "在营（开业）企业",
                "establish_date": "2023-05-09",
                "org_type": "有限责任公司(自然人投资或控股)",
                "reg_institute": "珠海市金湾区市场监督管理局",
                "keyword": "91440404MACG01YR3Q",
                "credit_cde": "91440404MACG01YR3Q",
                "realtime_ts": 1712214751,
                "report_2023_check_ts": -1
            },
            "try_id": 0,
            "meta": None,
            "weight": 150
        },
        "spider": {
            "receive_ts": -1,
            "send_ts": -1,
            "item_insert": False,
            "ab_info": {}
        },
        "crawler": {},
        "parser": {},
        "fusion": {},
        "dims": {},
        "channel": {}
    }
)
crawler = Crawler()
parser = Parser()

crawler.crawl(eventlog)
parser.parse(eventlog)
logger.info(f'after parser {eventlog.model_dump_json()}')
