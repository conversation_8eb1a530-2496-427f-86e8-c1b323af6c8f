import time
from urllib3 import disable_warnings
from resx.log import setup_logger
from resx.mysql_client import MySQLClient
from resx.config import *
import re
import os

logger = setup_logger(use_crawler_log=True, crawler_log_enable=False, name=__name__, debug=False)
from eventlog_spider.crawler.crawler_cods import CodsCrawler as Crawler
from eventlog_spider.parser.parser_cods import CodsParser as Parser
from eventlog_spider.common.eventlog_unify import Eventlog

disable_warnings()
mysql = MySQLClient(**CFG_MYSQL_GS_OUTER)
history_name_dao = MySQLClient(**CFG_MYSQL_GS_OUTER)

os.environ['POD_ENV'] = 'online'


def get_eventlog(name):
    return Eventlog.from_dict(
        {
            "event_id": "octopus_entry-FObCfcjZ-013d611331a2ee3127a516e3cdfa8afa-cods-1755677533589#1#1#1#1#1",
            "code": -1,
            "selector": {
                "reason": "user_update",
                "crawler_id": "FObCfcjZ",
                "crawler_name": "cods",
                "dimension_name": "新机构",
                "score": 0.1755677534,
                "from_cache": False,
                "receive_time": "2025-08-20 16:29:11",
                "send_time": "2025-08-20 16:29:11",
                "try_id": 1,
                "info": {
                    "unified_social_credit_code": "11410400744073608N",
                    "org_name": "",
                    "org_source": "cods"
                }
            },
            "crawler": {
                "code": -1,
                "receive_time": "1755678443",
                "send_time": "",
                "model": False,
                "error_msg": ""
            },
            "parser": {
                "code": -1,
                "receive_time": "",
                "send_time": "",
                "model": False,
                "error_msg": "",
                "data": {}
            },
            "fusion": {
                "code": -1,
                "receive_time": "",
                "send_time": "",
                "model": False,
                "error_msg": "",
                "diff": {}
            },
            "channel": {
                "debug_mode": False
            },
            "dimensions": {}
        }
    )


crawler = Crawler()
parser = Parser()

list_ = ['N2500233MF5990925N']
temp = []
for i in list_:
    # if i.startswith('11'):
    #     temp.append(i)
    #     continue
    a = get_eventlog(i)
    crawler.crawl(a)
    parser.parse(a)
    logger.info(f'after parser {a.model_dump_json()}')
print(temp)
