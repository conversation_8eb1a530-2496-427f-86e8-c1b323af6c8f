from resx.log import setup_logger
import os

logger = setup_logger(use_crawler_log=True, crawler_log_enable=False, name=__name__, debug=False)
from eventlog_spider.crawler.crawler_gov_unit import GovUnitCrawler as Crawler
from eventlog_spider.parser.parser_gov_unit import GovUnitParser as Parser
from eventlog_spider.common.eventlog_unify import Eventlog

os.environ['POD_ENV'] = 'online'

crawler = Crawler()
parser = Parser()


def get_eventlog():
    return Eventlog.from_dict(
        {
            "event_id": "octopus_entry-aZyWII8E-fff917a39a3e9c591ae1e48375b056f6-gov_unit-1754903507679",
            "code": -1,
            "selector": {
                "reason": "normal_schedule",
                "crawler_id": "aZyWII8E",
                "crawler_name": "gov_unit",
                "dimension_name": "事业单位",
                "score": 1.1754903508,
                "from_cache": False,
                "receive_time": "2025-08-11 17:11:47",
                "send_time": "2025-08-11 17:11:47",
                "try_id": 0,
                "info": {
                    "name": "北京市延庆区张山营镇龙聚山庄敬老院",
                    "us_credit_code": "12110229MB00013450"
                }
            },
            "crawler": {
                "code": -1,
                "receive_time": "",
                "send_time": "",
                "model": False
            },
            "parser": {
                "code": -1,
                "receive_time": "",
                "send_time": "",
                "model": False
            },
            "fusion": {
                "code": -1,
                "receive_time": "",
                "send_time": "",
                "model": False
            },
            "channel": {},
            "dimensions": {}
        }
    )


a = get_eventlog()
crawler.crawl(a)
parser.parse(a)
logger.info(f'after parser {a.model_dump_json()}')
