import os

from resx.log import setup_logger
from urllib3 import disable_warnings

logger = setup_logger(use_crawler_log=True, crawler_log_enable=False, name=__name__, debug=False)
from eventlog_spider.crawler.crawler_gds import GdsCraw<PERSON> as Crawler
from eventlog_spider.parser.parser_gds import GdsParser as Parser
from eventlog_spider.common.eventlog_unify import Eventlog

disable_warnings()
os.environ['POD_ENV'] = 'online'


def get_eventlog():
    return Eventlog.from_dict(
        {
            "event_id": "octopus_entry-Zxc1vZ5H-6304577942-gds_company-1754028084789",
            "code": -1,
            "selector": {
                "reason": "normal_schedule",
                "crawler_id": "Zxc1vZ5H",
                "crawler_name": "gds_company",
                "dimension_name": "商品信息公司",
                "score": 1.1754028085,
                "from_cache": False,
                "receive_time": "2025-08-01 14:01:24",
                "send_time": "2025-08-01 14:01:24",
                "try_id": 0,
                "info": {
                    "credit_no": "91110117MA0013L20C",
                    "company_id": "41388346",
                    "company_name": "北京清岚农业生态休闲发展有限公司",
                    "graph_id": "2317692655"
                }
            },
            "crawler": {
                "code": -1,
                "receive_time": "",
                "send_time": "",
                "model": False
            },
            "parser": {
                "code": -1,
                "receive_time": "",
                "send_time": "",
                "model": False
            },
            "fusion": {
                "code": -1,
                "receive_time": "",
                "send_time": "",
                "model": False
            },
            "channel": {},
            "dimensions": {}
        }
    )


crawler = Crawler()
parser = Parser()
a = get_eventlog()
crawler.crawl(a)
parser.parse(a)
logger.info(f'after parser {a.model_dump_json()}')
