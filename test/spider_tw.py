import os
import time

from resx.config import *
from resx.log import setup_logger
from urllib3 import disable_warnings

logger = setup_logger(use_crawler_log=True, crawler_log_enable=False, name=__name__, debug=False)
from eventlog_spider.crawler.crawler_tw import <PERSON>w<PERSON><PERSON><PERSON> as <PERSON>raw<PERSON>
from eventlog_spider.parser.parser_tw import Tw<PERSON><PERSON><PERSON> as Parser
from eventlog_spider.common.eventlog_unify import Eventlog
from resx.redis_types import Redis
from resx.kafka_client import KafkaProducerClient

disable_warnings()
os.environ['POD_ENV'] = 'online'
redis = Redis(**CFG_REDIS_GS, db=9)
fusion_producer_client = KafkaProducerClient(bootstrap_servers='kafka.middleware.huawei:9092', kafka_topic='basic_crawler_feedback')
crawler = Crawler()
parser = Parser()


def get_eventlog():
    return Eventlog.from_dict(
        {"event_id": "octopus_entry-0oysZ9uW-ff57496a41eec92dac6e4d9dd7710410-tw-1755166212695", "code": -1,
         "selector": {"reason": "normal_schedule", "crawler_id": "0oysZ9uW", "crawler_name": "tw", "dimension_name": "台湾", "score": 1.1755166213,
                      "from_cache": False, "receive_time": "2025-08-14 18:10:12", "send_time": "2025-08-14 18:10:12", "try_id": 0,
                      "info": {"code": "16299792", "name": "昱勝食品有限公司"}}, "crawler": {"code": -1, "receive_time": "", "send_time": "", "model": False},
         "parser": {"code": -1, "receive_time": "", "send_time": "", "model": False},
         "fusion": {"code": -1, "receive_time": "", "send_time": "", "model": False}, "channel": {}, "dimensions": {}}
    )


eventlog = get_eventlog()
crawler.crawl(eventlog)
parser.parse(eventlog)
logger.info(f'after parser {eventlog.model_dump_json()}')
