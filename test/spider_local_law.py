from urllib3 import disable_warnings
from crawler_log.log_init_v2 import Log
import logging
import os
from resx.log import setup_logger
import re

os.environ['POD_ENV'] = 'online'

# logger = Log('eventlog-crawler', env='other', domain='eventlog-crawler', dimension='eventlog-crawler', level=logging.INFO)
logger = setup_logger(use_crawler_log=True, crawler_log_enable=False, name=__name__, debug=False)

from eventlog_spider.crawler.crawler_law import Crawler<PERSON><PERSON> as Crawler
from eventlog_spider.parser.parser_law import <PERSON><PERSON><PERSON><PERSON><PERSON> as Parser
from eventlog_spider.common.eventlog_unify import Eventlog


def get_eventlog():
    return Eventlog.from_dict(
        {
            "event_id": "octopus_entry-sMJuAAD5-febbcae65e30831cc68433d92803e699-law-1754903548153",
            "code": -1,
            "selector": {
                "reason": "normal_schedule",
                "crawler_id": "sMJuAAD5",
                "crawler_name": "law",
                "dimension_name": "律所",
                "score": 1.1754903548,
                "from_cache": False,
                "receive_time": "2025-08-11 17:12:28",
                "send_time": "2025-08-11 17:12:28",
                "try_id": 0,
                "info": {
                    "creditcode": "31120000083023813Q",
                    "lawfirmname": "天津文懋律师事务所"
                }
            },
            "crawler": {
                "code": -1,
                "receive_time": "",
                "send_time": "",
                "model": False
            },
            "parser": {
                "code": -1,
                "receive_time": "",
                "send_time": "",
                "model": False
            },
            "fusion": {
                "code": -1,
                "receive_time": "",
                "send_time": "",
                "model": False
            },
            "channel": {},
            "dimensions": {}
        }
    )


crawler = Crawler()
parser = Parser()
eventlog = get_eventlog()
crawler.crawl(eventlog)
parser.parse(eventlog)
