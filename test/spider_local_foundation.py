from urllib3 import disable_warnings
from resx.log import setup_logger
import os

logger = setup_logger(use_crawler_log=True, crawler_log_enable=False, name=__name__, debug=False)
from eventlog_spider.crawler.crawler_foundation import FoundationCrawler as Crawler
from eventlog_spider.parser.parser_foundation import FoundationParser as Parser
from eventlog_spider.common.eventlog_unify import Eventlog

disable_warnings()
os.environ['POD_ENV'] = 'online'

eventlog = Eventlog.from_dict(
    {"event_id": "octopus_entry-lvdYutR1-ff78a71bb0e7ae4d3004ff9dfbb3dbaa-foundation-1754903447581", "code": -1,
     "selector": {"reason": "normal_schedule", "crawler_id": "lvdYutR1", "crawler_name": "foundation", "dimension_name": "基金会", "score": 1.1754903448,
                  "from_cache": False, "receive_time": "2025-08-11 17:10:47", "send_time": "2025-08-11 17:10:47", "try_id": 0,
                  "info": {"creditcode": "53640000MJX173429L", "name": "宁夏回族自治区见义勇为基金会"}},
     "crawler": {"code": -1, "receive_time": "", "send_time": "", "model": False}, "parser": {"code": -1, "receive_time": "", "send_time": "", "model": False},
     "fusion": {"code": -1, "receive_time": "", "send_time": "", "model": False}, "channel": {}, "dimensions": {}}
)

crawler = Crawler()
parser = Parser()

crawler.crawl(eventlog)
parser.parse(eventlog)
logger.info(f'after parser {eventlog.model_dump_json()}')
