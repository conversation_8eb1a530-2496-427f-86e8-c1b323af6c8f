from resx.log import setup_logger
import os

logger = setup_logger(use_crawler_log=True, crawler_log_enable=False, name=__name__, debug=False)
from eventlog_spider.crawler.crawler_hk import <PERSON><PERSON><PERSON><PERSON><PERSON> as Crawler
from eventlog_spider.parser.parser_hk import <PERSON><PERSON><PERSON><PERSON><PERSON> as Parser
from eventlog_spider.common.eventlog_unify import Eventlog

os.environ['POD_ENV'] = 'online'


def get_eventlog():
    return Eventlog.from_dict({"event_id": "octopus_entry-sixPQ7Q2-ffcfc177054fc532e30641c6087539de-hk-1754903467528", "code": -1,
                               "selector": {"reason": "normal_schedule", "crawler_id": "sixPQ7Q2", "crawler_name": "hk", "dimension_name": "香港",
                                            "score": 1.1754903468, "from_cache": False, "receive_time": "2025-08-11 17:11:07",
                                            "send_time": "2025-08-11 17:11:07", "try_id": 0, "info": {"br_num": "34389141"}},
                               "crawler": {"code": -1, "receive_time": "", "send_time": "", "model": False},
                               "parser": {"code": -1, "receive_time": "", "send_time": "", "model": False},
                               "fusion": {"code": -1, "receive_time": "", "send_time": "", "model": False}, "channel": {}, "dimensions": {}})


crawler = Crawler()
parser = Parser()

eventlog = get_eventlog()
crawler.crawl(eventlog)
parser.parse(eventlog)
logger.info(f'after parser {eventlog.model_dump_json()}')
